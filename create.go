package igame

import (
	"bytes"
	"encoding/gob"
	"fmt"
	_ "igame/bin/generated"
	"igame/modules"
	"igameCommon/basic"
	"igameCommon/snapshoot"
	"igameCommon/utils"
	"sort"
)

var (
	cache = map[int32]*snapData{}
)

type snapData struct {
	module basic.IModule
	table  map[int32][][2]uint32
}

func InitFromSnapshoot(gameID int32, ctl ...int32) {
	if _, ok := cache[gameID]; !ok {
		cache[gameID] = &snapData{table: map[int32][][2]uint32{}}
	}
	if cache[gameID].module == nil {
		cache[gameID].module = GetModuleFromSnapshoot(gameID)
	}
	for _, id := range ctl {
		cache[gameID].table[id] = GetTableFromSnapshoot(gameID, id)
	}
}

func GetModuleFromSnapshoot(gameID int32) basic.IModule {
	snap := snapshoot.Get(fmt.Sprintf("M%d", gameID))
	b := utils.DecompressFromBase64(snap)
	buf := bytes.NewBuffer(b)
	dec := gob.NewDecoder(buf)
	m := modules.Factory.Create(gameID)
	err := dec.Decode(m)
	if err != nil {
		panic(fmt.Sprintf("gameID:%d,err:%v", gameID, err))
	}
	return m
}

func GetTableFromSnapshoot(gameID int32, ctl int32) [][2]uint32 {
	snap := snapshoot.Get(fmt.Sprintf("GM%dCTL%d", gameID, ctl))
	if len(snap) == 0 {
		panic("snapshoot empty")
	}
	b := utils.DecompressFromBase64(snap)
	buf := bytes.NewBuffer(b)
	dec := gob.NewDecoder(buf)
	var table [][2]uint32
	err := dec.Decode(&table)
	if err != nil {
		panic(err)
	}
	return table
}

func Spin(gameID int32, ctl int32, n int64, tax float64) (basic.ISpin, int32) {
	module := cache[gameID].module
	table := cache[gameID].table[ctl]
	a, b := int64(n&0xFFFFFFFF), int64(n>>32)
	lines := module.Line()
	minPayout := module.MinPayout(ctl)
	coef := module.InputCoef(ctl)
	pay := float64(minPayout) / float64(lines) * 100 / float64(coef)
	a = int64(float64(a) * (1 - pay) / (1 - pay - tax))
	if a >= int64(table[len(table)-1][0]) {
		minSpin := module.MinSpin(ctl, a)
		return module.Salting(minSpin, b), coef
	}
	index := sort.Search(len(table), func(i int) bool {
		return int64(table[i][0]) >= a
	})
	spinID := int64(table[index][1])
	return module.Salting(module.Spin(spinID), b), coef
}

func DebugSpin(gameID int32, spinID int64) basic.ISpin {
	module := cache[gameID].module
	return module.Spin(spinID)
}

func ZeroSpin(gameID int32, ctl int32, n int64, ctx basic.SpinContext) (basic.ISpin, int32) {
	module := cache[gameID].module
	a, b := int64(n&0xFFFFFFFF), int64(n>>32)
	return module.Salting(module.ZeroSpin(ctl, a), b), module.InputCoef(ctl)
}

func SpinRule(gameID int32, ctx basic.SpinContext) string {
	return cache[gameID].module.Rule(ctx)
}

func SpinRule_(gameID int32, ctx basic.SpinContext) string {
	m := modules.Factory.Create(gameID)
	return m.Rule(ctx)
}

func Line(gameID int32) int32 {
	return cache[gameID].module.Line()
}

func Line_(gameID int32) int32 {
	m := modules.Factory.Create(gameID)
	return m.Line()
}

func ClientMode(gameID int32) int32 {
	// m := modules.Factory.Create(gameID)
	// m.ClientMode()
	return cache[gameID].module.ClientMode()
}

func InputCoef(gameID int32, ctl int32) int32 {
	return cache[gameID].module.InputCoef(ctl)
}

func SpinIDs(gameID int32) []int64 {
	return utils.MapKeysOrdered(cache[gameID].module.PayTable())
}

func Payout(gameID int32, ctl int32, n int64, tax float64) int32 {
	module := cache[gameID].module
	table := cache[gameID].table[ctl]
	a, _ := int64(n&0xFFFFFFFF), int64(n>>32)
	minPayout := module.MinPayout(ctl)
	lines := module.Line()
	coef := module.InputCoef(ctl)
	pay := float64(minPayout) / float64(lines) * 100 / float64(coef)
	a = int64(float64(a) * (1 - pay) / (1 - pay - tax))
	if a >= int64(table[len(table)-1][0]) {
		return minPayout
	}
	index := sort.Search(len(table), func(i int) bool {
		return int64(table[i][0]) >= a
	})
	return module.Spin(int64(table[index][1])).Payout()
}

func Exception(gameID int32, code int32) string {
	return cache[gameID].module.Exception(code)
}
