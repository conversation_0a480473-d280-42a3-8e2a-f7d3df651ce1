package modules

import (
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

type c10000015 struct {
	Row         int           // 网格行数
	Column      int           // 网格列数
	PayoutTable map[int32]int // 赔率表
	IconWeightA map[int32]int // 图标权重A
	IconWeightB map[int32]int // 图标权重B
}

var _ = Factory.reg(basic.NewGeneral[*m10000015])

type m10000015 struct {
	Config                c10000015
	RandByWeightA         *utils.RandomWeightPicker[int32, int]
	RandByWeightAWithout0 *utils.RandomWeightPicker[int32, int]
	RandByWeightB         *utils.RandomWeightPicker[int32, int]
	RandByWeightBWithout0 *utils.RandomWeightPicker[int32, int]
}

func (m *m10000015) Init(config []byte) {
	m.Config = utils.ParseYAML[c10000015](config)
	m.RandByWeightA = utils.NewRandomWeightPicker(m.Config.IconWeightA)
	m.RandByWeightB = utils.NewRandomWeightPicker(m.Config.IconWeightB)
	AWithout0 := make(map[int32]int)
	BWithout0 := make(map[int32]int)
	for k, v := range m.Config.IconWeightA {
		if k != 0 {
			AWithout0[k] = v
		}
	}
	for k, v := range m.Config.IconWeightB {
		if k != 0 {
			BWithout0[k] = v
		}
	}

	m.RandByWeightAWithout0 = utils.NewRandomWeightPicker(AWithout0)
	m.RandByWeightBWithout0 = utils.NewRandomWeightPicker(BWithout0)
}

func (m10000015) ID() int32 {
	return 10000015
}

func (m m10000015) Line() int32 { // 线数
	return 1
}

func (m m10000015) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m10000015) Exception(code int32) string {
	return games.S10000015{}.Exception(code)
}

func (m *m10000015) genGrid(rd *rand.Rand, idx int) []int32 {
	grid := make([]int32, m.Config.Row*m.Config.Column)
	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Row + col
			switch idx {
			case 0:
				grid[index] = m.RandByWeightAWithout0.One(rd)
				if row == 1 {
					grid[index] = m.RandByWeightA.One(rd)
				}
			case 1:
				grid[index] = m.RandByWeightBWithout0.One(rd)
				if row == 1 {
					grid[index] = m.RandByWeightB.One(rd)
				}
			}
		}
	}
	return grid
}

func (m *m10000015) Spin(rd *rand.Rand) basic.ISpin {
	spin := games.S10000015{}
	gridA := m.genGrid(rd, 0)
	gridB := m.genGrid(rd, 1)
	spin.Grids = append(spin.Grids, gridA, gridB)

	pays, lines := m.parsePayout(spin.Grids)
	spin.Pays = int32(pays)
	spin.Lines = lines

	return &spin
}

func (m *m10000015) parsePayout(grids [][]int32) (int, []games.L10000015) {
	pays := 0
	var lines []games.L10000015
	// 假设每个grid为一维数组，且每个grid长度等于Row*Column
	for idx, grid := range grids {
		if len(grid) < 6 {
			continue // 长度不足，跳过
		}
		// 以3、4、5号位为中奖线
		if grid[3] == grid[4] && grid[3] == grid[5] {
			pays += m.Config.PayoutTable[grid[3]]
			lines = append(lines, games.L10000015{
				Positions: []int{3, 4, 5},
				Icon:      int16(grid[3]),
				Value:     int32(m.Config.PayoutTable[grid[3]]),
				Owner:     idx,
			})
		} else if grid[3] == 0 || grid[4] == 0 || grid[5] == 0 {
			// 有0则不中奖
			continue
		} else {
			pays += m.Config.PayoutTable[0]
			lines = append(lines, games.L10000015{
				Positions: []int{3, 4, 5},
				Icon:      int16(grid[3]),
				Value:     int32(m.Config.PayoutTable[0]),
				Owner:     idx,
			})
		}
	}
	if len(lines) > 1 {
		pays *= 2 // 如果有多条线中奖，翻倍
	}
	return pays, lines
}

func (m *m10000015) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (m *m10000015) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	r := spin.(*games.S10000015)
	r.GameId = m.ID()
	r.Line = m.Line()
	r.Row = m.Config.Row
	r.Column = m.Config.Column
	return r
}

func (m10000015) Rule(ctx map[string]any) string {
	return ""
}

func (m10000015) InputCoef(ctl int32) int32 {
	switch ctl {
	case 1:
		return 100
	case 2:
		return 100
	case 3:
		return 200
	default:
		return 100
	}
}

func (m m10000015) MinPayout(ctl int32) int32 {
	return 0
}
