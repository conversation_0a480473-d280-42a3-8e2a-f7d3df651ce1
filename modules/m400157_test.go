package modules

import (
	"fmt"
	"math/rand"
	"os"
	"testing"
)

func TestM400157(t *testing.T) {
	config, _ := os.ReadFile("../bin/configs/400157.yaml")
	m := m400157{}
	m.Init(config)
	for i := range [1000]int{} {
		fmt.Println(i)
		rd := rand.New(rand.NewSource(int64(i)))
		gridSize := m.Config.Row * m.Config.Column
		grid := make([]int16, gridSize)
		for i := 0; i < gridSize; i++ {
			grid[i] = -1
		}

		for row := 0; row < m.Config.Row; row++ {
			for col := 0; col < m.Config.Column; col++ {
				index := row*m.Config.Column + col
				grid[index] = m.RandByWeight.One(rd)
			}
		}
		for i := 0; i < gridSize; i += 5 {
			fmt.Println(grid[i : i+5])
		}
		fmt.Println("===============")

		var gridCol [][]int
		for col := 0; col < m.Config.Column; col++ {
			tmp := make([]int, 0)
			for row := 0; row < m.Config.Row; row++ {
				tmp = append(tmp, int(grid[row*m.Config.Column+col]))
			}
			gridCol = append(gridCol, tmp)
		}

		for c := range gridCol {
			for r := range gridCol[c] {
				if r == 1 && gridCol[c][0] != int(m.Config.WildIcon) {
					if gridCol[c][r] == int(m.Config.WildIcon) {
						gridCol[c][r+1] = 0
					}
				} else if r == 2 && gridCol[c][len(gridCol[c])-1] != int(m.Config.WildIcon) {
					if gridCol[c][r] == int(m.Config.WildIcon) {
						gridCol[c][r-1] = 0
					}
				}
			}
		}

		for c := range gridCol {
			fmt.Println(gridCol[c])
		}

		fmt.Println("===============")
		for c := range gridCol {
			for r := range gridCol[c] {
				if r == len(gridCol[c])-1 && gridCol[c][r] == int(m.Config.WildIcon) {
					gridCol[c] = append(gridCol[c], 0)
					if len(gridCol[c]) > m.Config.Row {
						gridCol[c] = gridCol[c][1:]
					}
				} else if r == 0 && gridCol[c][r] == int(m.Config.WildIcon) {
					gridCol[c] = append([]int{0}, gridCol[c]...)
					if len(gridCol[c]) > m.Config.Row {
						gridCol[c] = gridCol[c][:len(gridCol[c])-1]
					}
				} else {
					if r == 1 && gridCol[c][0] != int(m.Config.WildIcon) {
						if gridCol[c][r] == int(m.Config.WildIcon) {
							gridCol[c][r+1] = 0
						}
					} else if r == 2 && gridCol[c][len(gridCol[c])-1] != int(m.Config.WildIcon) {
						if gridCol[c][r] == int(m.Config.WildIcon) {
							gridCol[c][r-1] = 0
						}
					}
				}
			}
		}

		for c := range gridCol {
			fmt.Println(gridCol[c])
		}
	}

}

// 基准测试：原始实现
func BenchmarkOriginalGridProcessing(b *testing.B) {
	config, _ := os.ReadFile("../bin/configs/400157.yaml")
	m := m400157{}
	m.Init(config)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		rd := rand.New(rand.NewSource(int64(i)))
		gridSize := m.Config.Row * m.Config.Column
		grid := make([]int16, gridSize)

		// 原始方式：逐个生成
		for row := 0; row < m.Config.Row; row++ {
			for col := 0; col < m.Config.Column; col++ {
				index := row*m.Config.Column + col
				grid[index] = m.RandByWeight.One(rd)
			}
		}

		// 原始方式：使用append转换列
		var gridCol [][]int
		for col := 0; col < m.Config.Column; col++ {
			tmp := make([]int, 0)
			for row := 0; row < m.Config.Row; row++ {
				tmp = append(tmp, int(grid[row*m.Config.Column+col]))
			}
			gridCol = append(gridCol, tmp)
		}
	}
}

// 基准测试：优化后的实现（保持原有逻辑）
func BenchmarkOptimizedGridProcessing(b *testing.B) {
	config, _ := os.ReadFile("../bin/configs/400157.yaml")
	m := m400157{}
	m.Init(config)

	// 预分配缓冲区
	gridSize := m.Config.Row * m.Config.Column
	gridBuffer := make([]int16, gridSize)
	colBuffers := make([][]int, m.Config.Column)
	for i := range colBuffers {
		colBuffers[i] = make([]int, m.Config.Row)
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		rd := rand.New(rand.NewSource(int64(i)))

		// 优化方式：批量生成
		grid := m.RandByWeight.More(gridSize, rd)
		copy(gridBuffer, grid)

		// 优化方式：使用预分配缓冲区转换列
		for col := 0; col < m.Config.Column; col++ {
			colBuffer := colBuffers[col]
			for row := 0; row < m.Config.Row; row++ {
				colBuffer[row] = int(gridBuffer[row*m.Config.Column+col])
			}
		}
	}
}
