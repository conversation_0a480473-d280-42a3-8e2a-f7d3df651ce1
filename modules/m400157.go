package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

// 新生成的游戏项目
// 游戏ID: 400157
// 游戏名称: WinterThuder
// 作者: noir
// 生成时间: 2025-08-18 14:28:16

// 配置结构体
type c400157 struct {
	MaxPayout   int64              `yaml:"maxPayout"`   // 最大赔付
	Row         int                `yaml:"row"`         // 行数
	Column      int                `yaml:"column"`      // 列数
	Pattern     [][]basic.Position `yaml:"pattern"`     // 连线模式
	PayoutTable map[int16][]int64  `yaml:"payoutTable"` // 赔付表
	IconWeight  map[int16]int32    `yaml:"iconWeight"`  // 图标权重
	WildIcon    int16              `yaml:"wildIcon"`    // 百搭图标
	ScatterIcon int16              `yaml:"scatterIcon"` // 散布图标
	MoonIcon    int16              `yaml:"moonIcon"`    // 月亮图标
	MinLimit    map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}

// 游戏模块结构体
var _ = Factory.reg(basic.NewGeneral[*m400157])

type m400157 struct {
	Config       c400157
	RandByWeight *utils.RandomWeightPicker[int16, int32]
}

func (m *m400157) Init(config []byte) {
	m.Config = utils.ParseYAML[c400157](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
}

func (m m400157) ID() int32 {
	return 400157
}

func (m m400157) Line() int32 {
	return 25
}

func (m m400157) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m400157) Exception(code int32) string {
	s := &games.S400157{}
	return s.Exception(code)
}

func (m *m400157) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m400157) Spin(rd *rand.Rand) basic.ISpin {
	// 生成网格
	grid := m.generateGridView(rd)

	// 创建旋转结果
	spin := &games.S400157{
		Pays: 0,
	}

	page := games.P400157{
		Grid: grid,
	}
	spin.Pages = append(spin.Pages, page)
	// 计算赔付
	m.calculatePayout(spin)

	return spin
}

func (m *m400157) generateGridView(rd *rand.Rand) []int16 {
	gridSize := m.Config.Row * m.Config.Column
	grid := m.RandByWeight.More(gridSize, rd)

	return grid
}

func (m *m400157) processWild(grid []int16) []int16 {
	// 补充 grid 中的 wild
	return grid
}

func (m *m400157) calculatePayout(spin *games.S400157) {
	// TODO: 实现赔付计算逻辑
	// 这里需要根据具体游戏规则实现
	spin.Pays = 0
}

func (m *m400157) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S400157)
	s.GameId = m.ID()
	s.Line = m.Line()
	s.Row = m.Config.Row
	s.Column = m.Config.Column

	// 最大赔付限制
	if s.Pays > int32(m.Config.MaxPayout) {
		s.Pays = int32(m.Config.MaxPayout)
	}

	return s
}

func (m m400157) Rule(ctx map[string]any) string {
	ruleData := map[string]any{
		"analInfo": map[string]any{
			"VIP_maxWinFreq_big":   2183612,
			"VIP_maxWinFreq_small": 104508,
			"addBonusK":            []int{10000, 1000, 50, 20},
			"addBonusNames":        []string{"wolfThunder_grand", "wolfThunder_major", "wolfThunder_minor", "wolfThunder_mini"},
			"arrlimits_winLimitK":  []int{5000, 12000},
			"baseIncuts":           []int{1, 2, 3, 4, 5, 6, 7, 8},
			"baseReels": []any{[]int{8, 8, 8, 9, 7, 7, 7, 1, 5, 5, 10, 10, 4, 4, 6, 6, 6, 6, 1, 7, 7, 7, 2, 2, 5, 5, 5, 3, 3, 0, 0, 2, 5, 5, 5, 5, 1, 1, 7, 7, 4, 4, 6, 6, 6, 3, 8, 8, 8, 9, 7, 7, 7, 1, 5, 5, 4, 4, 6, 6, 6, 6, 1, 7, 7, 7, 2, 2, 5, 5, 5, 3, 3, 0, 0, 2, 5, 5, 5, 5, 1, 1, 7, 7, 4, 4, 6, 6, 6, 3}, []int{7, 7, 3, 10, 10, 8, 8, 8, 8, 1, 1, 5, 5, 10, 10, 5, 4, 4, 8, 8, 8, 9, 5, 5, 5, 3, 0, 0, 10, 10, 5, 5, 5, 9, 4, 8, 8, 8, 2, 5, 5, 5, 1, 1, 10, 10, 6, 6, 6, 4, 4, 7, 7, 3, 8, 8, 8, 8, 1, 1, 5, 5, 5, 4, 4, 8, 10, 10, 8, 8, 9, 5, 5, 5, 3, 0, 0, 5, 5, 5, 9, 4, 8, 8, 8, 2, 10, 10, 5, 5, 5, 1, 1, 6, 6, 6, 4, 4}, []int{4, 8, 8, 8, 8, 10, 10, 2, 2, 7, 7, 7, 7, 3, 3, 10, 10, 8, 8, 8, 8, 9, 7, 7, 7, 2, 8, 8, 4, 4, 6, 6, 6, 3, 5, 5, 5, 10, 10, 1, 7, 7, 7, 0, 0, 6, 6, 6, 4, 4, 7, 7, 7, 4, 8, 8, 8, 8, 2, 2, 7, 7, 7, 7, 3, 3, 10, 10, 10, 8, 8, 8, 8, 9, 7, 7, 7, 2, 8, 8, 4, 4, 6, 6, 6, 3, 0, 0, 6, 6, 6, 4, 4, 7, 7, 7}, []int{6, 6, 6, 3, 8, 8, 8, 8, 1, 7, 7, 5, 5, 5, 2, 2, 0, 0, 6, 6, 6, 9, 8, 8, 8, 3, 3, 10, 10, 10, 10, 5, 5, 5, 1, 1, 6, 6, 6, 4, 4, 8, 8, 2, 5, 5, 5, 10, 10, 10, 5, 6, 6, 6, 3, 8, 8, 8, 8, 1, 10, 10, 7, 7, 5, 5, 5, 2, 2, 0, 0, 6, 6, 6, 9, 8, 8, 8, 3, 3, 10, 10, 10, 5, 5, 5, 1, 1, 6, 6, 6, 4, 4, 10, 10, 8, 8, 2, 5, 5, 5, 5}, []int{2, 2, 7, 7, 7, 10, 10, 7, 3, 5, 5, 5, 3, 3, 9, 8, 8, 8, 8, 2, 10, 10, 5, 5, 5, 5, 1, 1, 0, 0, 7, 7, 4, 4, 10, 10, 6, 6, 6, 6, 3, 8, 8, 8, 8, 1, 5, 5, 10, 10, 10, 10, 5, 2, 2, 7, 7, 7, 7, 3, 5, 5, 5, 3, 10, 10, 3, 9, 8, 8, 8, 8,
				2, 5, 5, 5, 5, 1, 1, 0, 0, 7, 7, 4, 4, 6, 6, 6, 6, 3, 10, 10, 8, 8, 8, 8, 1, 5, 5, 5}},
			"bonusValues":      []int{1, 2, 3, 4, 5},
			"bonusValuesBase":  []int{0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 14, 16, 18},
			"bonusValuesSuper": []int{20, 22, 24, 25, 30, 40, 50, 60, 80, 100, -12, -13, -14, -15},
			"formula": map[string]any{
				"args": []string{"betPerLine", "nlines"},
				"body": "return(betPerLine * nlines/1)",
			},
			"formulaReverse": map[string]any{
				"args": []string{"betPerGame", "nlines"},
				"body": "return(betPerGame / nlines*1)",
			},
			"freeIncuts":       []int{1, 2, 3, 4, 5, 6, 7, 8},
			"freeReels":        []any{[]int{8, 8, 8, 9, 7, 7, 7, 1, 5, 5, 4, 4, 6, 6, 6, 6, 1, 7, 7, 7, 2, 2, 5, 5, 5, 3, 3, 0, 0, 2, 5, 5, 5, 5, 1, 1, 7, 7, 4, 4, 6, 6, 6, 3}, []int{7, 7, 3, 8, 8, 8, 8, 1, 1, 5, 5, 5, 4, 4, 8, 8, 8, 9, 5, 5, 5, 3, 0, 0, 5, 5, 5, 9, 4, 8, 8, 8, 2, 5, 5, 5, 1, 1, 6, 6, 6, 4, 4}, []int{4, 8, 8, 8, 9, 8, 2, 2, 7, 7, 7, 9, 7, 3, 3, 8, 8, 8, 8, 9, 7, 7, 7, 2, 8, 8, 9, 4, 4, 6, 6, 6, 3, 5, 5, 5, 1, 9, 7, 7, 7, 0, 0, 6, 6, 6, 9, 4, 4, 7, 7, 7}, []int{6, 6, 6, 3, 9, 8, 8, 8, 8, 1, 7, 9, 7, 5, 5, 5, 2, 2, 9, 0, 0, 6, 6, 6, 9, 8, 8, 8, 3, 3, 9, 5, 5, 5, 1, 1, 6, 6, 6, 9, 4, 4, 8, 8, 2, 5, 5, 5, 5}, []int{2, 2, 7, 9, 7, 7, 7, 3, 9, 5, 5, 5, 3, 3, 9, 8, 8, 8, 8, 2, 5, 5, 5, 5, 1, 1, 9, 0, 0, 7, 7, 4, 4, 6, 6, 6, 9, 6, 3, 8, 8, 8, 8, 1, 5, 5, 5}},
			"incutIds":         []int{16},
			"lineStyles":       []any{[]int{2, 2, 2, 2, 2}, []int{1, 1, 1, 1, 1}, []int{3, 3, 3, 3, 3}, []int{0, 0, 0, 0, 0}, []int{0, 1, 2, 1, 0}, []int{3, 2, 1, 2, 3}, []int{1, 2, 3, 2, 1}, []int{2, 1, 0, 1, 2}, []int{1, 1, 0, 1, 1}, []int{2, 2, 3, 2, 2}, []int{0, 0, 1, 0, 0}, []int{3, 3, 2, 3, 3}, []int{0, 1, 0, 1, 0}, []int{3, 2, 3, 2, 3}, []int{1, 0, 1, 0, 1}, []int{2, 3, 2, 3, 2}, []int{0, 1, 1, 1, 0}, []int{3, 2, 2, 2, 3}, []int{2, 3, 3, 3, 2}, []int{1, 0, 0, 0, 1}, []int{2, 1, 2, 1, 2}, []int{1, 2, 1, 2, 1}, []int{1, 2, 2, 2, 1}, []int{1, 1, 2, 1, 1}, []int{2, 2, 1, 2, 2}},
			"maxWinFreq_big":   9205002,
			"maxWinFreq_small": 277841,
			"minScatters":      []int{3},
			"outRates_vipmode": 96.11,
			"sasAdditionalId":  "WTH",
			"sasPaytableId":    "WTH960",
			"scatterIds":       []int{9},
			"statTablo": map[string]any{
				"bigwin":     9,
				"bonus":      7,
				"epicwin":    9,
				"rtp":        96.03,
				"show":       1,
				"volatility": 9,
			},
			"symbolNames": []string{"wolf", "bear", "deer", "puma", "eagle", "symb_a", "symb_k", "symb_q", "symb_j", "scatter", "moon"},
			"volatility":  4.5,
			"wildIds":     []int{0},
		},
		"antiDynamiteBet": nil,
		"aux":             0,
		"bbLimitsWinK":    []int{11505, 5000, 12000, 11505},
		"betAssortment":   ctx["betAssortment"],
		"betPerGame":      ctx["input"],
		"betPerLine":      ctx["betPerLine"],
		"bonus": map[string]any{
			"boxIn":   []any{[]int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}},
			"boxOut":  []any{[]int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}},
			"boxWork": nil,
		},
		"denomAssortment_cents":  []int{1},
		"doubleActive":           "off",
		"doubleActiveDbSettings": "off",
		"doubleAssortment":       []string{"off"},
		"dramshow":               nil,
		"gamegroup":              "base",
		"gcurrency":              "",
		"gdenom":                 1,
		"helpInfo": map[string]any{
			"doubles": []any{[]any{"off", 0, 0}},
			"fg": map[string]any{
				"limit": 40,
			},
			"paytable": []any{[]any{[]int{0, 1}, []int{5, 500}, []int{4, 200}, []int{3, 100}}, []any{[]int{1, 4}, []int{5, 300}, []int{4, 150}, []int{3, 75}}, []any{[]int{2, 4}, []int{5, 250}, []int{4, 125}, []int{3, 50}}, []any{[]int{3, 4}, []int{5, 250}, []int{4, 125}, []int{3, 50}}, []any{[]int{4, 4}, []int{5, 200}, []int{4, 100}, []int{3, 25}}, []any{[]int{5, 4}, []int{5, 75}, []int{4, 25}, []int{3, 10}}, []any{[]int{6, 4}, []int{5, 50}, []int{4, 20}, []int{3, 8}}, []any{[]int{7, 4}, []int{5, 50}, []int{4, 20}, []int{3, 8}}, []any{[]int{8, 4}, []int{5, 25}, []int{4, 10}, []int{3, 5}}, []any{[]int{9, 8}}, []any{[]int{10, 8}}, []any{[]int{11, 4}}, []any{[]int{12, 4}}, []any{[]int{13, 4}}, []any{[]int{14, 4}}, []any{[]int{15, 4}}, []any{[]int{16, 16}}},
		},
		"helpseed":                true,
		"incutId":                 2,
		"isMaxFlag":               0,
		"isMaxFlag_lines":         0,
		"linesAssortment":         []int{25},
		"linesPerCredit":          1,
		"maxBetPerGame_cents":     nil,
		"maxBetPerGame_credits":   20000000,
		"minBetPerGame_cents":     nil,
		"nlines":                  25,
		"outRatesVolatility":      nil,
		"phaseCur":                "finished",
		"phaseNext":               "toIdle",
		"placedbet":               ctx["input"],
		"present":                 "no",
		"reelstate":               0,
		"saveBoxIn":               []any{[]int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}},
		"setVip_inFreeSpinAlways": -1,
		"shotEffect":              false,
		"shotMask":                []any{[]int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0}},
		"startBox":                []any{[]int{2, 2, 1, 5, 0}, []int{2, 2, 5, 2, 0}, []int{2, 0, 5, 2, 2}, []int{2, 0, 7, 8, 2}},
		"stopBox":                 []any{[]int{2, 2, 1, 5, 0}, []int{2, 2, 5, 2, 0}, []int{2, 0, 5, 2, 2}, []int{2, 0, 7, 8, 2}},
		"tmpWin":                  0,
		"versions": map[string]any{
			"server_core": "1.1",
			"server_game": "1.0",
			"server_math": "1.0",
		},
		"vipMode": map[string]any{
			"on":             1,
			"vipBetK":        2,
			"vip_noSpecSeed": true,
			"wasBuyVip":      2,
		},
		"winValidation": map[string]any{
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
			"needcheck":                    false,
			"period":                       86400000,
			"remaintime":                   86400000,
			"winlimit_fictiveRotate_gcurr": 25000000,
		},
		"winlimits": map[string]any{
			"maxWinLimitK":         12000,
			"maxWin_gcurr":         nil,
			"needControlJackpot":   true,
			"winLimitK_gameconfig": 12000,
		},
	}

	b, _ := json.Marshal(ruleData)
	return string(b)
}

func (m m400157) InputCoef(ctl int32) int32 {
	return 100
}

func (m m400157) MinPayout(ctl int32) int32 {
	mode, ok := m.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * m.Line()
}
