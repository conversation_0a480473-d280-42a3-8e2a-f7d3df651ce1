package modules

import (
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

type c200001 struct {
	GameID           int                 // 游戏ID
	RuleID           int                 // 生成器规则ID
	Row              int32               // 网格行数
	Column           int32               // 网格列数
	MaxPayout        int32               // 最大支付
	PayoutTable      map[int16][]float64 // 支付表，key 为图标 ID，value 为对应的支付数组
	IconWeight       map[int16]int       // 图标权重
	FreeIconWeight   map[int16]int       // 免费模式图标权重
	MultiplierIcon   int16               // 倍率图标
	MultiplierWeight map[int16]int       // 倍率权重
}

var _ = Factory.reg(basic.NewGeneral[*m200001])

type m200001 struct {
	Config           c200001
	RandByWeight     *utils.RandomWeightPicker[int16, int]
	RandByFreeWeight *utils.RandomWeightPicker[int16, int]
	MultiplierWeight *utils.RandomWeightPicker[int16, int]
}

func (m *m200001) Init(config []byte) {
	m.Config = utils.ParseYAML[c200001](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
	m.RandByFreeWeight = utils.NewRandomWeightPicker(m.Config.FreeIconWeight)
	m.MultiplierWeight = utils.NewRandomWeightPicker(m.Config.MultiplierWeight)
}

func (m200001) ID() int32 {
	return 200001
}

func (m m200001) Line() int32 { // 线数
	return 20
}

func (m m200001) ClientMode() int32 {
	return basic.EnumClientMode.MULTI
}

func (m m200001) Exception(code int32) string {
	return games.S200001{}.Exception(code)
}

type Grid struct {
	ico int16
	mul int16
}

// 生成初始网格
func (m *m200001) genGrid(rd *rand.Rand, isFree bool) []Grid {
	grid := make([]Grid, m.Config.Row*m.Config.Column)
	if isFree {
		for i := 0; i < int(m.Config.Row*m.Config.Column); i++ {
			ico := m.RandByFreeWeight.One(rd)
			var mul int16 = 0
			if ico == m.Config.MultiplierIcon {
				mul = m.MultiplierWeight.One(rd)
			}
			grid[i] = Grid{
				ico: ico,
				mul: mul,
			}
		}
	} else {
		for i := 0; i < int(m.Config.Row*m.Config.Column); i++ {
			grid[i] = Grid{
				ico: m.RandByWeight.One(rd),
				mul: 0,
			}
		}
	}
	return grid
}

// 执行级联消除
func (m *m200001) performCascade(grid []Grid, rd *rand.Rand, spin *games.S200001) ([]Grid, []games.L200001) {
	newGrid := make([]Grid, len(grid))
	for i := range grid {
		newGrid[i] = Grid{ico: grid[i].ico, mul: grid[i].mul}
	}

	lines := m.parsePayout(newGrid, spin)

	// 移除获胜符号
	removed := make(map[int]bool)
	for _, line := range lines {
		for _, pos := range line.Positions {
			removed[pos] = true
		}
	}

	// 更新网格：移除的符号置为 0，新符号掉落
	for col := 0; col < int(m.Config.Column); col++ {
		newCol := make([]Grid, 0, m.Config.Row)
		// 收集非零符号（从下往上）
		for row := int(m.Config.Row) - 1; row >= 0; row-- {
			idx := row*int(m.Config.Column) + col
			if !removed[idx] {
				newCol = append(newCol, newGrid[idx])
			}
		}
		// 用新符号填充空位
		for len(newCol) < int(m.Config.Row) {
			var ico int16 = 0
			var mul int16 = 0
			if spin.IsFreeMode {
				ico = m.RandByFreeWeight.One(rd)
				if ico == 12 { // 明确检查符号 ID 为 12
					mul = m.MultiplierWeight.One(rd)
				}
			} else {
				ico = m.RandByWeight.One(rd)
			}
			newCol = append(newCol, Grid{ico: ico, mul: mul})
		}
		// 倒序填回网格（从下往上）
		for row := int(m.Config.Row) - 1; row >= 0; row-- {
			idx := row*int(m.Config.Column) + col
			newGrid[idx] = newCol[int(m.Config.Row)-1-row]
		}
	}
	return newGrid, lines
}

// 解析支付信息
func (m *m200001) parsePayout(grid []Grid, spin *games.S200001) []games.L200001 {
	iconCounts := make(map[int16]int)
	iconPositions := make(map[int16][]int)
	lines := make([]games.L200001, 0)
	id := 0

	// 统计每个 icon 的出现次数、位置和倍率
	for i, g := range grid {
		iconCounts[g.ico]++
		iconPositions[g.ico] = append(iconPositions[g.ico], i)
	}

	// 处理每个 icon 的 payout
	for icon, count := range iconCounts {
		// 检查 PayoutTable 中是否存在该 icon 和对应的 count
		if payoutData, exists := m.Config.PayoutTable[icon]; exists {
			payout := payoutData[count+1]
			if payout > 0 {
				// 进入免费模式
				if icon == 1 {
					spin.IsFree = true
					spin.FreeSpin = 10
				} else if icon == 1 && spin.IsFreeMode {
					spin.FreeSpin = 5
				}

				// 计算倍率
				lines = append(lines, games.L200001{
					ID:        id,
					Positions: iconPositions[icon],
					Icon:      icon,
					Count:     int(count),
					Value:     float64(payout),
				})
				id++
			}
		}
	}

	return lines
}

// 执行旋转逻辑
func (m *m200001) Spin(rd *rand.Rand) basic.ISpin {
	spin := &games.S200001{
		Pages:  []*games.P200001{},
		Rounds: []*games.S200001{},
		Line:   m.Line(),
	}

	// 生成初始网格并执行级联消除
	grid := m.genGrid(rd, false)
	for {
		page := &games.P200001{
			Grid:  make([]int16, len(grid)),
			Lines: []games.L200001{},
		}
		for i, g := range grid {
			page.Grid[i] = g.ico
		}
		newGrid, lines := m.performCascade(grid, rd, spin)
		if len(lines) == 0 {
			spin.Pages = append(spin.Pages, page)
			break
		}

		page.Lines = lines
		for _, line := range lines {
			page.Pay += line.Value
		}
		spin.Pays += int32(page.Pay)
		spin.Pages = append(spin.Pages, page)
		grid = newGrid
	}
	spin.Rounds = append(spin.Rounds, spin)

	// 如果触发免费旋转，生成免费旋转轮次
	if spin.IsFree {
		for i := 0; i < int(spin.FreeSpin); i++ {
			freeSpin := &games.S200001{
				Pays:       0,
				Pages:      []*games.P200001{},
				IsFree:     true,
				IsFreeMode: true,
			}
			grid := m.genGrid(rd, true)
			for {
				page := &games.P200001{
					Grid:           make([]int16, len(grid)),
					Lines:          []games.L200001{},
					MultiplierData: []games.Mul200001{}, // 初始化
				}
				for i, g := range grid {
					page.Grid[i] = g.ico
				}

				var totalMultiplier int32 = 0 // 默认倍率
				for i, g := range grid {
					if g.ico == 12 {
						page.MultiplierData = append(page.MultiplierData, games.Mul200001{
							ID:        int(g.ico),
							Positions: i,
							Value:     g.mul,
						})
						page.Multiplier += int32(g.mul)
						totalMultiplier += int32(g.mul)
					}
				}

				newGrid, lines := m.performCascade(grid, rd, freeSpin)
				if len(lines) == 0 {
					freeSpin.Pages = append(freeSpin.Pages, page)
					break
				}
				page.Lines = lines
				freeSpin.Multiplier = totalMultiplier

				for _, line := range lines {
					page.Pay += line.Value * float64(totalMultiplier) // 应用倍率
				}
				freeSpin.Pays += int32(page.Pay)
				freeSpin.Pages = append(freeSpin.Pages, page)
				grid = newGrid
			}
			spin.Rounds = append(spin.Rounds, freeSpin)
		}
	}

	return spin
}

func (m *m200001) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rand.New(rd))
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m200001) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	n := salt.Int31n(100000)
	if n >= 99990 {
		return games.S200001{
			Pays: n,
		}
	}
	return games.S200001{
		Pays: 0,
	}
}

func (m200001) Rule(ctx map[string]any) string {
	return ""
}

func (m200001) InputCoef(ctl int32) int32 {
	return 100
}

func (m m200001) MinPayout(int32) int32 {
	return 0
}
