package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
	"slices"
)

var _ = Factory.reg(basic.NewGeneral[*m10000126])

type m10000126 struct {
	Config       c10000126
	RandByWeight *utils.RandomWeightPicker[int16, int]
}

func (m m10000126) ID() int32 {
	return 10000126
}

func (m *m10000126) Line() int32 {
	return 5
}

func (m m10000126) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m10000126) Exception(code int32) string {
	return games.S10000126{}.Exception(code)
}

func (m *m10000126) Init(config []byte) {
	m.Config = utils.ParseYAML[c10000126](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
}

func (m m10000126) ZeroSpin(ctl int32, salt *rand.Rand) basic.ISpin {
	spin := games.S10000126{Pages: make([][]int16, 1)}
	for i := 0; i < 1000; i++ {
		spin.Pages[0] = utils.RandomFromWeighted(m.Config.IconWeight, 9, salt)
		if pay, _, _ := m.parsePayout(spin.Pages[0]); pay == 0 {
			return &spin
		}
	}
	return &spin
}

func (m m10000126) Spin(rd *rand.Rand) basic.ISpin {
	var spin games.S10000126
	if rd.Int31n(100) < m.Config.SpecialProb {
		spin = m.genSpec(rd)
	} else {
		spin = m.genNorm(rd)
	}

	// 全命中
	if spin.Tenfold {
		spin.Pays *= 10
	}

	if spin.Pays > m.Config.MaxPayout {
		spin.Pays = -1
	}
	return &spin
}

func (m *m10000126) genNorm(rd *rand.Rand) games.S10000126 {
	grid := m.RandByWeight.More(9, rd)
	spin := games.S10000126{
		Pages: [][]int16{grid},
	}
	spin.Pays, spin.Lines, spin.Tenfold = m.parsePayout(grid)
	return spin
}

func (m m10000126) genSpec(rd *rand.Rand) games.S10000126 {
	var grid []int16
	var hit int16
	for range 100 {
		grid = m.RandByWeight.More(9, rd)
		if utils.Sum(grid) == 0 { // 非法
			return games.S10000126{Pays: -1}
		}
		pay, lines, _ := m.parsePayout(grid)
		if pay <= 0 {
			continue
		}
		hit = lines[0][0]
		if hit != 0 {
			break
		}
	}
	if hit == 0 {
		return games.S10000126{Pays: -1}
	}
	keep := make([]int16, 9)
	for i := 0; i < 9; i++ {
		keep[i] = -1
	}
	for i, v := range grid {
		if v == hit || v == 0 {
			keep[i] = v
		}
	}
	spin := games.S10000126{
		Pages: [][]int16{keep},
		Hit:   hit,
	}
	for i := 0; i < 9; i++ {
		keep = slices.Clone(keep)
		grid = m.RandByWeight.More(9, rd)
		var count int
		for i, v := range grid {
			if (v == hit || v == 0) && keep[i] == -1 {
				keep[i] = v
				count++
			}
		}
		spin.Pages = append(spin.Pages, keep)
		need := 0
		for _, v := range keep {
			if v == -1 {
				need++
			}
		}
		if count == 0 || need == 0 {
			break
		}
	}
	spin.Pays, spin.Lines, spin.Tenfold = m.parsePayout(keep)

	return spin
}

func (m m10000126) parsePayout(grid []int16) (payout int32, lines [][]int16, tenfold bool) {
	var flag uint64
	for idx, pos := range m.Config.Pattern {
		x, y, z := pos[0].Index(3), pos[1].Index(3), pos[2].Index(3)
		a, b, c := grid[x], grid[y], grid[z]
		ico, pay := m.checkLine(a, b, c)
		if pay <= 0 {
			continue
		}
		lines = append(lines, []int16{ico, int16(idx), pay, int16(x), int16(y), int16(z)})
		payout += int32(pay)
		flag |= 1 << x
		flag |= 1 << y
		flag |= 1 << z
	}
	if flag == 0x1FF {
		tenfold = true
	}
	return payout, lines, tenfold
}

func (m m10000126) checkLine(a, b, c int16) (int16, int16) {
	count := map[int16]int{}
	count[a]++
	count[b]++
	count[c]++
	delete(count, m.Config.CommonIcon)
	if len(count) == 0 {
		return m.Config.CommonIcon, m.Config.PayoutTable[m.Config.CommonIcon]
	}
	if len(count) == 1 {
		for k := range count {
			return k, m.Config.PayoutTable[k]
		}
	}
	return -1, 0
}

func (m m10000126) Salting(spin0 basic.ISpin, salt *rand.Rand) basic.ISpin {
	spin := spin0.(*games.S10000126)
	spin.GameId = m.ID()
	spin.Line = m.Line()
	spin.Pattern = m.Config.Pattern
	return spin
}

func (m m10000126) Rule(ctx map[string]any) string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

func (m m10000126) InputCoef(mod int32) int32 {
	return 100
}

type c10000126 struct {
	Pattern     [][]basic.Position
	PayoutTable map[int16]int16
	CommonIcon  int16
	SpecialProb int32
	MaxPayout   int32
	IconWeight  map[int16]int
}

func (m m10000126) MinPayout(ctl int32) int32 {
	return 0
}
