package modules

import (
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

type c300001 struct {
	MaxPayout          int32
	Row                int // 网格行数
	Column             int // 网格列数
	PayoutTable        map[int16][]int16
	Pattern            [][]basic.Position
	IconWeight         map[int16]int
	WildCountWeight    map[int16]int
	WildIcon           int16
	Wild2Icon          int16
	BonusIcon          int16
	GoldTreeProb       int32
	SpecialPayoutTable map[int16][]int16
	SpecialWeight      map[int16]int
}

// Example General

var _ = Factory.reg(basic.NewGeneral[*m300001])

type m300001 struct {
	Config                c300001
	RandByWeight          *utils.RandomWeightPicker[int16, int]
	RandByWildCountWeight *utils.RandomWeightPicker[int16, int]
	RandBySpecialWeight   *utils.RandomWeightPicker[int16, int]
}

func (m *m300001) Init(config []byte) {
	m.Config = utils.ParseYAML[c300001](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
	m.RandByWildCountWeight = utils.NewRandomWeightPicker(m.Config.WildCountWeight)
	m.RandBySpecialWeight = utils.NewRandomWeightPicker(m.Config.SpecialWeight)
}

func (m300001) ID() int32 {
	return 300001
}

func (m m300001) Line() int32 { // 线数
	return 10
}

func (m m300001) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m300001) Exception(code int32) string {
	return "{}"
}

func (m *m300001) Spin(rd *rand.Rand) basic.ISpin {
	grid := m.RandByWeight.More(m.Config.Row*m.Config.Column, rd)
	spin := games.S300001{
		Grid: grid,
	}

	bonusCols := []int{0, 2, 4}
	bonusInCol := make(map[int]bool)
	for row := 0; row < m.Config.Row; row++ {
		for _, col := range bonusCols {
			pos := row*m.Config.Column + col
			if grid[pos] == m.Config.BonusIcon {
				bonusInCol[col] = true
			}
		}
	}
	// 特殊模式
	specMap := make(map[int16]int)
	if bonusInCol[0] && bonusInCol[2] && bonusInCol[4] {
		spin.Special.Times = rd.Intn(7) + 8
		spin.Special.Lists = m.RandBySpecialWeight.More(spin.Special.Times, rd)
		for _, list := range spin.Special.Lists {
			maxLen := len(m.Config.SpecialPayoutTable[list])
			if maxLen == 0 {
				continue
			}
			if specMap[list] < maxLen-1 {
				specMap[list]++
			}
		}
		for spec, c := range specMap {
			maxLen := len(m.Config.SpecialPayoutTable[spec])
			if maxLen == 0 {
				continue
			}
			if c >= maxLen {
				c = maxLen - 1
			}
			payoutValue := m.Config.SpecialPayoutTable[spec][c]
			spin.Special.Muls += int(payoutValue)
		}
		spin.Pays = int32(spin.Special.Muls)
		spin.IsSpecial = true
		return &spin
	}

	// 摇钱树模式
	if rd.Int31n(100) < m.Config.GoldTreeProb {
		wildCount := m.RandByWildCountWeight.One(rd)
		total := m.Config.Row * m.Config.Column
		indices := make([]int, total)
		for i := 0; i < total; i++ {
			indices[i] = i
		}
		for i := total - 1; i > 0; i-- {
			j := rd.Intn(i + 1)
			indices[i], indices[j] = indices[j], indices[i]
		}
		wildPos := indices[:wildCount]
		spin.Wilds = wildPos
		spin.IsGoldTree = true
	}
	if spin.IsGoldTree {
		spin.Lines = m.parsePayout(grid, spin.Wilds)
	} else {
		spin.Lines = m.parsePayout(grid, nil)
	}

	for _, line := range spin.Lines {
		spin.Pays += line.Value
	}
	return &spin
}

func (m m300001) parsePayout(grid []int16, wilds []int) []games.L300001 {
	// 将grid转换为按行排列的二维数组，方便访问
	gridRows := make([][]int16, m.Config.Row)
	for row := 0; row < m.Config.Row; row++ {
		gridRows[row] = make([]int16, m.Config.Column)
		for col := 0; col < m.Config.Column; col++ {
			gridRows[row][col] = grid[row*m.Config.Column+col]
		}
	}

	// 如果有wilds，将对应位置的图标替换为wildIcon
	if len(wilds) > 0 {
		for _, pos := range wilds {
			row := pos/m.Config.Column + 1
			col := pos%m.Config.Column + 1
			if row < m.Config.Row && col < m.Config.Column {
				gridRows[row][col] = m.Config.WildIcon
			}
		}
	}

	// 存储所有符合条件的连线
	var lines []games.L300001

	// 遍历所有连线模式
	for lineID, pattern := range m.Config.Pattern {
		// 获取第一个位置的图标
		firstPos := pattern[0]
		row := int(firstPos.Row())
		col := int(firstPos.Column())

		// 确保位置在有效范围内
		if row >= 0 && row < m.Config.Row && col >= 0 && col < m.Config.Column {
			icon := gridRows[row][col]

			// 计算连续匹配的数量
			count := 1
			positions := []int{row*m.Config.Column + col}

			// 检查后续位置
			for i := 1; i < len(pattern); i++ {
				pos := pattern[i]
				r := int(pos.Row())
				c := int(pos.Column())

				// 确保位置在有效范围内
				if r < 0 || r >= m.Config.Row || c < 0 || c >= m.Config.Column {
					break
				}

				// 获取当前位置的图标
				currentIcon := gridRows[r][c]

				// 如果当前图标与起始图标相同或者是百搭，则匹配成功
				// Bonus图标不参与连线
				if currentIcon == icon ||
					(currentIcon == m.Config.Wild2Icon && icon != m.Config.BonusIcon) ||
					(currentIcon == m.Config.WildIcon && icon != m.Config.Wild2Icon && icon != m.Config.BonusIcon) {
					count++
					positions = append(positions, r*m.Config.Column+c)
				} else {
					break
				}
			}

			// 如果连线长度至少为3，计算赔付
			if count >= 3 {
				// 获取该图标对应的赔付值
				payoutValues, exists := m.Config.PayoutTable[icon]
				if !exists {
					continue
				}

				// 确保索引在有效范围内
				if count-1 < len(payoutValues) {
					payout := payoutValues[count-1]

					// 创建连线记录
					line := games.L300001{
						ID:        lineID + 1, // 连线ID从1开始
						Positions: positions,
						Icon:      icon,
						Count:     count,
						Value:     int32(payout),
					}

					lines = append(lines, line)
				}
			}
		}
	}

	return lines
}

func (m *m300001) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (m *m300001) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S300001)
	s.GameId = m.ID()
	s.Line = m.Line()
	s.Row = m.Config.Row
	s.Column = m.Config.Column
	return s
}

func (m300001) Rule(ctx map[string]any) string {
	return ""
}

func (m300001) InputCoef(int32) int32 {
	return 100
}

func (m m300001) MinPayout(int32) int32 {
	return 0
}
