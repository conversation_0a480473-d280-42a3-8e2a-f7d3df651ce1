// m100005.go
package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
	"slices"
)

var _ = Factory.reg(basic.NewGeneral[*m100005])

type m100005 struct {
	Config       c100005
	RandByWeight *utils.RandomWeightPicker[int32, int32]
}

type c100005 struct {
	Row         int32
	Column      int32
	PayoutTable [][4]int32 // [id, minCount, maxCount, pay]
	FreeSpin    struct {
		Icon        basic.Ico
		FirstNumber int
		FirstCount  int
		MoreNumber  int
		MoreCount   int
	}
	CoefTable  map[basic.Ico]int32
	MaxPayout  int32
	IconWeight map[int32]int32
	MinLimit   map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}

func (m *m100005) ID() int32 {
	return 100005
}

func (m *m100005) Line() int32 {
	return 20
}

func (m m100005) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m100005) Exception(code int32) string {
	return games.S100005{}.Exception(code)
}

func (m *m100005) Init(config []byte) {
	m.Config = utils.ParseYAML[c100005](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
}

func (m *m100005) ZeroSpin(ctl int32, salt *rand.Rand) basic.ISpin {
	switch ctl {
	case 0, 2:
		for {
			spin := m.Spin(salt)
			pay := spin.Payout()
			if pay == 0 {
				return spin
			}
		}
	case 1:
		pages := make([]games.P100005, 0, m.Config.FreeSpin.FirstCount+1)
		pages = append(pages, games.P100005{
			DropIco: [][]int32{m.zeroPage(salt)},
		})
		for len(pages) < m.Config.FreeSpin.FirstCount+1 {
			spin := m.Spin(salt).(games.S100005)
			for _, page := range spin.Pages {
				if page.BasicPays == 0 {
					pages = append(pages, page)
				}
			}
		}
		pages = pages[:m.Config.FreeSpin.FirstCount+1]
		for i := 0; i < m.Config.FreeSpin.FirstNumber; i++ {
			pages[0].DropIco[0][i] = int32(m.Config.FreeSpin.Icon)
		}
		utils.Shuffle(pages[0].DropIco[0], salt)
		tab := m.Config.PayoutTable[slices.IndexFunc(m.Config.PayoutTable, func(item [4]int32) bool {
			return item[0] == int32(m.Config.FreeSpin.Icon) && item[1] == int32(m.Config.FreeSpin.FirstNumber)
		})]
		return games.S100005{
			Pays:  tab[3],
			Pages: pages,
		}
	default:
		panic("")
	}
}

func (m *m100005) zeroPage(salt *rand.Rand) (result []int32) {
	for i := int32(3); i <= 11; i++ {
		for j := 0; j < 7; j++ {
			result = append(result, i)
		}
	}
	utils.Shuffle(result, salt)
	return result[:m.Config.Column*m.Config.Row]
}

func (m *m100005) Spin(rd *rand.Rand) basic.ISpin {
	var spin games.S100005

	// 生成普通旋转
	page := m.generatePage(rd)
	if page.BasicPays == -1 {
		return games.S100005{Pays: -1}
	}
	totalPay := page.BasicPays * page.Coef
	if totalPay > m.Config.MaxPayout {
		return games.S100005{Pays: -1}
	}

	spin = games.S100005{
		Pays:  totalPay,
		Pages: []games.P100005{page},
	}

	// 检查免费旋转
	var freeSpins int
	if count, ok := page.Special[m.Config.FreeSpin.Icon]; ok && count >= m.Config.FreeSpin.FirstNumber {
		freeSpins = m.Config.FreeSpin.FirstCount
	}

	// 处理免费旋转
	for i := 0; i < int(freeSpins); i++ {
		page = m.generatePage(rd)
		if page.BasicPays == -1 {
			return games.S100005{Pays: -1}
		}
		totalPay = page.BasicPays * page.Coef
		if totalPay > m.Config.MaxPayout {
			return games.S100005{Pays: -1}
		}

		spin.Pages = append(spin.Pages, page)
		spin.Pays += totalPay

		// 检查额外免费旋转
		if count, ok := page.Special[m.Config.FreeSpin.Icon]; ok && count >= m.Config.FreeSpin.MoreNumber {
			freeSpins += m.Config.FreeSpin.MoreCount
		}
	}

	return spin
}

func (m *m100005) generatePage(rd *rand.Rand) games.P100005 {
	// 初始图标数量
	currentCounts := make(map[basic.Ico]int)
	initialIcons := m.RandByWeight.More(int(m.Config.Row*m.Config.Column), rd)
	for _, ico := range initialIcons {
		currentCounts[basic.Ico(ico)]++
	}

	var basicPays int32
	removes := []map[basic.Ico]int{}
	dropIcos := [][]int32{initialIcons}

	// 消除与掉落循环
	for {
		hasElimination := false
		currentRemove := make(map[basic.Ico]int)

		// 检查消除，排除宙斯
		for _, rule := range m.Config.PayoutTable {
			id, minCount, maxCount, pay := rule[0], rule[1], rule[2], rule[3]
			if basic.Ico(id) == m.Config.FreeSpin.Icon {
				continue // 跳过宙斯，不消除
			}
			count, exists := currentCounts[basic.Ico(id)]
			if exists && count >= int(minCount) && count <= int(maxCount) {
				basicPays += pay
				hasElimination = true
				currentRemove[basic.Ico(id)] = count
				currentCounts[basic.Ico(id)] = 0 // 消除后清零
			}
		}

		if hasElimination {
			removes = append(removes, currentRemove)

			// 计算消除总数并补充新图标
			eliminatedCount := 0
			for _, count := range currentRemove {
				eliminatedCount += int(count)
			}
			newIcons := m.RandByWeight.More(eliminatedCount, rd)
			dropIcos = append(dropIcos, newIcons)

			// 更新当前图标数量
			for _, ico := range newIcons {
				currentCounts[basic.Ico(ico)]++
			}
		} else {
			break
		}
	}

	// 统计剩余的宙斯和 Coef 图标，并计算宙斯倍数
	special := make(map[basic.Ico]int)
	var coef int32
	for ico, count := range currentCounts {
		if ico == m.Config.FreeSpin.Icon || m.Config.CoefTable[ico] > 0 {
			special[ico] = count
			if coefVal, ok := m.Config.CoefTable[ico]; ok {
				coef += coefVal * int32(count) // 按数量累乘系数
			}
		}
	}

	// 检查宙斯的倍数
	for _, rule := range m.Config.PayoutTable {
		id, minCount, maxCount, pay := rule[0], rule[1], rule[2], rule[3]
		if basic.Ico(id) == m.Config.FreeSpin.Icon {
			count, exists := currentCounts[m.Config.FreeSpin.Icon]
			if exists && count >= int(minCount) && count <= int(maxCount) {
				if basicPays > 0 || coef > 0 { // 宙斯不能中其他
					return games.P100005{
						BasicPays: -1,
					}
				}
				basicPays += pay // 加上宙斯的倍数
			}
		}
	}

	if coef == 0 {
		coef = 1 // 确保最低系数为 1
	}

	return games.P100005{
		BasicPays: basicPays,
		Remove:    removes,
		Special:   special,
		Coef:      coef,
		DropIco:   dropIcos,
	}
}

func (m *m100005) Salting(spin0 basic.ISpin, salt *rand.Rand) basic.ISpin {
	spin := spin0.(games.S100005)

	for i := range spin.Pages {
		page := &spin.Pages[i]
		for _, item := range page.DropIco {
			utils.Shuffle(item, salt)
		}
	}
	return spin
}

func (m m100005) Rule(ctx map[string]any) string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

// 不同的算法模式的输入乘数
func (m m100005) InputCoef(ctl int32) int32 {
	switch ctl {
	default:
		return 100
	case 1:
		return 10000
	case 2:
		return 125
	}
}

// 不同的算法模式的最小赔率 单线
func (m m100005) MinPayout(ctl int32) int32 {
	mode, ok := m.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * m.Line()
}
