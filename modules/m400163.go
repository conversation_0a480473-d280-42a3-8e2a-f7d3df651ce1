package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"igameHttp/types/belatra"
	"math/rand"
)

type c400163 struct {
	MaxPayout       int64
	Line            int32
	Row             int
	Column          int
	HackProb        int
	NormScatterIcon int16
	HotScatterIcon  int16
	Pattern         [][]basic.Position
	PayoutTable     map[int16][]int64
	HackIconWeight  map[int16]int32
	NormIconWeight  map[int16]int32
	HotIconWeight   map[int16]int32
	WildMap         map[int16]int
	MinLimit        map[int32]struct {
		X     int32
		Limit map[string]int32
	}
}

// Example General

var _ = Factory.reg(basic.NewGeneral[*m400163])

type m400163 struct {
	Config                          c400163
	RandByHackIconWeight            *utils.RandomWeightPicker[int16, int32]
	RandByHackWithoutWild           *utils.RandomWeightPicker[int16, int32]
	RandByNormIconWeight            *utils.RandomWeightPicker[int16, int32]
	RandByHotIconWeight             *utils.RandomWeightPicker[int16, int32]
	RandByNormWithoutScatter        *utils.RandomWeightPicker[int16, int32]
	RandByHotWithoutScatter         *utils.RandomWeightPicker[int16, int32]
	RandByNormWithoutWild           *utils.RandomWeightPicker[int16, int32]
	RandByHotWithoutWild            *utils.RandomWeightPicker[int16, int32]
	RandByNormWithoutWildAndScatter *utils.RandomWeightPicker[int16, int32]
	RandByHotWithoutWildAndScatter  *utils.RandomWeightPicker[int16, int32]
}

func (m *m400163) Init(config []byte) {
	m.Config = utils.ParseYAML[c400163](config)
	m.RandByHackIconWeight = utils.NewRandomWeightPicker(m.Config.HackIconWeight)
	m.RandByNormIconWeight = utils.NewRandomWeightPicker(m.Config.NormIconWeight)
	m.RandByHotIconWeight = utils.NewRandomWeightPicker(m.Config.HotIconWeight)

	filter := func(src map[int16]int32, cond func(icon int16) bool) map[int16]int32 {
		dst := make(map[int16]int32)
		for icon, weight := range src {
			if cond(icon) {
				dst[icon] = weight
			}
		}
		return dst
	}

	// 过滤条件
	notScatter := func(icon int16) bool {
		return icon != m.Config.NormScatterIcon && icon != m.Config.HotScatterIcon
	}
	notWild := func(icon int16) bool {
		return icon < 14
	}
	notWildAndScatter := func(icon int16) bool {
		return icon < 14 && icon != m.Config.NormScatterIcon && icon != m.Config.HotScatterIcon
	}

	m.RandByNormWithoutScatter = utils.NewRandomWeightPicker(filter(m.Config.NormIconWeight, notScatter))
	m.RandByHotWithoutScatter = utils.NewRandomWeightPicker(filter(m.Config.HotIconWeight, notScatter))
	m.RandByNormWithoutWild = utils.NewRandomWeightPicker(filter(m.Config.NormIconWeight, notWild))
	m.RandByHotWithoutWild = utils.NewRandomWeightPicker(filter(m.Config.HotIconWeight, notWild))
	m.RandByHackWithoutWild = utils.NewRandomWeightPicker(filter(m.Config.HackIconWeight, notWild))
	m.RandByNormWithoutWildAndScatter = utils.NewRandomWeightPicker(filter(m.Config.NormIconWeight, notWildAndScatter))
	m.RandByHotWithoutWildAndScatter = utils.NewRandomWeightPicker(filter(m.Config.HotIconWeight, notWildAndScatter))
}

func (m400163) ID() int32 {
	return 400163
}

func (m m400163) Line() int32 { // 线数
	return 20
}

func (m m400163) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m400163) Exception(code int32) string {
	return "{}"
}

func (m m400163) genGrid(rd *rand.Rand) []int16 {
	grid := make([]int16, m.Config.Row*m.Config.Column)
	scatterCount := 0
	// 记录第1、3、5列的scatter个数（列索引从0开始，所以是索引0、2、4）
	scatterCountInCols135 := map[int]int{0: 0, 2: 0, 4: 0}

	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Column + col

			if scatterCount >= 3 {
				if col == 0 || col == 4 {
					// 第 0 列和第 4 列不出现百搭
					grid[index] = m.RandByNormWithoutWildAndScatter.One(rd)
				} else {
					grid[index] = m.RandByNormWithoutScatter.One(rd)
				}
			} else {
				if col == 0 || col == 4 {
					// 第 0 列和第 4 列不出现百搭
					// 检查第1、5列是否已有scatter，如果有则不再生成scatter
					if (col == 0 && scatterCountInCols135[0] >= 1) || (col == 4 && scatterCountInCols135[4] >= 1) {
						grid[index] = m.RandByNormWithoutWildAndScatter.One(rd)
					} else {
						grid[index] = m.RandByNormWithoutWild.One(rd)
					}
				} else if col == 1 || col == 3 {
					grid[index] = m.RandByNormWithoutScatter.One(rd)
				} else {
					// 第3列（索引为2）：检查是否已有scatter，如果有则不再生成scatter
					if col == 2 && scatterCountInCols135[2] >= 1 {
						grid[index] = m.RandByNormWithoutScatter.One(rd)
					} else {
						grid[index] = m.RandByNormIconWeight.One(rd)
					}
				}
			}

			// 检查生成的图标是否为scatter，并更新计数
			if grid[index] == m.Config.NormScatterIcon || grid[index] == m.Config.HotScatterIcon {
				scatterCount++
				// 如果是第1、3、5列，更新对应列的scatter计数
				if col == 0 || col == 2 || col == 4 {
					scatterCountInCols135[col]++
				}
			}
		}
	}

	return grid
}

func (m m400163) genHackGrid(rd *rand.Rand) []int16 {
	grid := make([]int16, m.Config.Row*m.Config.Column)
	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Column + col
			if col == 0 || col == 4 {
				grid[index] = m.RandByHackWithoutWild.One(rd)
			} else {
				grid[index] = m.RandByHackIconWeight.One(rd)
			}
		}
	}
	return grid
}

func (m m400163) genFreeGrid(rd *rand.Rand, heroId int16) []int16 {
	grid := make([]int16, m.Config.Row*m.Config.Column)

	// 创建动态调整权重的随机选择器
	randWithoutWildAndScatter := m.createDynamicWeightPicker(m.Config.NormIconWeight, heroId, true, true)
	randWithoutScatter := m.createDynamicWeightPicker(m.Config.NormIconWeight, heroId, false, true)

	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Column + col
			if col == 0 || col == 4 {
				// 第 0 列和第 4 列不出现百搭
				grid[index] = randWithoutWildAndScatter.One(rd)
			} else {
				grid[index] = randWithoutScatter.One(rd)
			}
		}
	}
	return grid
}

func (m m400163) genHotGrid(rd *rand.Rand, heroId int16) []int16 {
	grid := make([]int16, m.Config.Row*m.Config.Column)

	// 创建动态调整权重的随机选择器
	randWithoutWildAndScatter := m.createDynamicWeightPicker(m.Config.HotIconWeight, heroId, true, true)
	randWithoutScatter := m.createDynamicWeightPicker(m.Config.HotIconWeight, heroId, false, true)

	for row := 0; row < m.Config.Row; row++ {
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Column + col
			if col == 0 || col == 4 {
				// 第 0 列和第 4 列不出现百搭
				grid[index] = randWithoutWildAndScatter.One(rd)
			} else {
				grid[index] = randWithoutScatter.One(rd)
			}
		}
	}
	return grid
}

// createDynamicWeightPicker 创建动态调整权重的随机选择器
// baseWeight: 基础权重配置
// heroId: 英雄ID (0-3)，0表示不调整权重
// excludeWild: 是否排除百搭符号
// excludeScatter: 是否排除scatter符号
func (m *m400163) createDynamicWeightPicker(baseWeight map[int16]int32, heroId int16, excludeWild, excludeScatter bool) *utils.RandomWeightPicker[int16, int32] {
	// 创建临时权重配置
	tempWeight := make(map[int16]int32)

	// 复制基础权重
	for icon, weight := range baseWeight {
		tempWeight[icon] = weight
	}

	// 根据heroId调整对应图标的权重
	if heroId > 0 && heroId <= 4 {
		// heroId 1-3 对应图标ID 0-2
		targetIconId := heroId
		if originalWeight, exists := tempWeight[targetIconId]; exists {
			// 将对应图标的权重翻倍
			tempWeight[targetIconId] = originalWeight * 3
		}
	}

	// 应用过滤条件
	filteredWeight := make(map[int16]int32)
	for icon, weight := range tempWeight {
		include := true

		// 排除百搭符号 (icon >= 14)
		if excludeWild && icon >= 14 {
			include = false
		}

		// 排除scatter符号
		if excludeScatter && (icon == m.Config.NormScatterIcon || icon == m.Config.HotScatterIcon) {
			include = false
		}

		if include {
			filteredWeight[icon] = weight
		}
	}

	return utils.NewRandomWeightPicker(filteredWeight)
}

func (m *m400163) Spin(rd *rand.Rand) basic.ISpin {
	s := games.S400163{
		IsFree: false,
		IsHot:  false,
		IsHack: false,
	}
	var grid []int16
	if rd.Intn(100) < m.Config.HackProb {
		grid = m.genHackGrid(rd)
		s.IsHack = true
	} else {
		grid = m.genGrid(rd)
		s.IsHack = false
	}

	wilds := map[int]int16{}
	s.Wilds = m.parseWilds(grid, wilds)
	linesInfo := m.parsePayout(grid, s.Wilds, &s)
	s.Lines = linesInfo
	page := games.P400163{
		Grid:  grid,
		Wilds: s.Wilds,
		Lines: linesInfo,
		Pays:  s.Pays,
	}

	if s.IsFree || s.IsHot {
		heroId := rd.Intn(3) + 1
		freeSpins := rd.Intn(13) + 7
		page.FreeInfo.Total = freeSpins
		page.FreeInfo.Remain = freeSpins
		page.FreeInfo.Award = freeSpins
		page.FreeInfo.Nesting = 1
		page.FreeInfo.WildDir = 0
		page.FreeInfo.WildReelID.ChoicedHero = heroId
		page.FreeInfo.WildReelID.ChoicedNBox = rd.Intn(4)
		page.FreeInfo.WildReelID.Choiced = 0
		page.FreeInfo.WildReelID.FakeID = []int{rd.Intn(13) + 7, rd.Intn(13) + 7, rd.Intn(13) + 7}
		allWin := 0
		s.Pages = append(s.Pages, page)

		// 选择生成 grid 的函数
		var genGridFunc func(rd *rand.Rand, heroId int16) []int16
		if s.IsFree {
			genGridFunc = m.genFreeGrid
		} else {
			genGridFunc = m.genHotGrid
		}

		// freeSpins wilds 独立于主游戏 wilds
		wilds = cloneWilds(wilds) // 重新初始化
		for i := 0; i < freeSpins; i++ {
			grid = genGridFunc(rd, int16(heroId))
			wilds = m.parseWilds(grid, wilds) // 只在 freeSpins 内继承
			freePage := games.P400163{
				Grid:  grid,
				Wilds: cloneWilds(wilds),
				Lines: m.parsePayout(grid, wilds, &s),
				FreeInfo: belatra.FreeInfo{
					Total:   freeSpins,
					Remain:  freeSpins - i - 1,
					Award:   0,
					Nesting: 1,
					WildDir: 0,
					WildReelID: belatra.WildReelID{
						ChoicedHero: s.Pages[0].FreeInfo.WildReelID.ChoicedHero,
						ChoicedNBox: s.Pages[0].FreeInfo.WildReelID.ChoicedNBox,
						Choiced:     0,
						FakeID:      s.Pages[0].FreeInfo.WildReelID.FakeID,
					},
				},
			}
			for _, line := range freePage.Lines {
				freePage.Pays += int32(line.Iwin.Win)
				allWin += int(line.Iwin.Win)
			}
			page.FreeInfo.AllWin = allWin
			s.Pages = append(s.Pages, freePage)
		}
	} else {
		s.Pages = append(s.Pages, page)
	}

	return &s
}

func (m m400163) parseWilds(grid []int16, wilds map[int]int16) map[int]int16 {
	for i := 0; i < m.Config.Row; i++ {
		for j := 0; j < m.Config.Column; j++ {
			index := i*m.Config.Column + j
			if grid[index] > 14 {
				wilds[index] = grid[index]
			}
		}
	}
	return wilds
}

func (m m400163) parsePayout(grid []int16, wilds map[int]int16, s *games.S400163) []belatra.LinesInfo {
	scatterCount := 0
	hotScatterCount := 0
	gridRows := make([][]int16, m.Config.Row)

	for row := 0; row < m.Config.Row; row++ {
		gridRows[row] = make([]int16, m.Config.Column)
		for col := 0; col < m.Config.Column; col++ {
			index := row*m.Config.Column + col
			val := grid[index]
			if wilds != nil {
				if wild, ok := wilds[index]; ok {
					val = wild
				}
			}
			gridRows[row][col] = val

			// 只统计0,2,4列的scatter
			if col == 0 || col == 2 || col == 4 {
				if val == m.Config.NormScatterIcon {
					scatterCount++
				}
				if val == m.Config.HotScatterIcon {
					hotScatterCount++
				}
			}
		}
	}

	linesInfo := []belatra.LinesInfo{}
	for lineID, pattern := range m.Config.Pattern {
		symbols := make([]int16, 5)
		for i, pos := range pattern {
			row := pos.Row()
			col := pos.Column()
			if row < 0 || row >= 3 || col < 0 || col >= 5 {
				continue
			}
			symbols[i] = gridRows[row][col]
		}

		if ok, mul, payout, online := m.checkLine(symbols); ok {
			s.Pays += int32(payout * int64(mul))
			linesInfo = append(linesInfo, belatra.LinesInfo{
				ID: lineID,
				Iwin: belatra.Iwin{
					Cost:   payout,
					K:      mul,
					Win:    payout * int64(mul),
					OnLine: online,
				},
			})
		}
	}

	// 状态重置
	if hotScatterCount >= 3 {
		s.IsHot = true
		s.IsFree = false
	} else if scatterCount >= 3 || scatterCount+hotScatterCount >= 3 {
		s.IsFree = true
		s.IsHot = false
	}

	return linesInfo
}

func (m m400163) checkLine(symbols []int16) (bool, int, int64, []int16) {
	if len(symbols) < 3 {
		return false, 1, 0, nil
	}
	firstIcon := symbols[0]
	count := 0
	mul := 0
	for _, icon := range symbols {
		if firstIcon == m.Config.NormScatterIcon || firstIcon == m.Config.HotScatterIcon {
			break
		}
		if icon == firstIcon || (icon > 14 && icon != m.Config.NormScatterIcon && icon != m.Config.HotScatterIcon) {
			count++
		} else {
			break
		}
	}
	if count >= 3 {
		for _, icon := range symbols {
			if icon > 14 {
				mul += m.Config.WildMap[icon]
			}
		}
		payout := m.Config.PayoutTable[firstIcon][count]
		online := make([]int16, len(symbols))
		copy(online, symbols[:count])
		for i := 0; i < len(symbols); i++ {
			if online[i] == 0 {
				online[i] = 127
			}
		}

		if mul == 0 {
			mul = 1
		}
		return true, mul, payout, online
	}
	return false, 1, 0, nil
}

func (m *m400163) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (m *m400163) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	s := spin.(*games.S400163)
	s.GameId = m.ID()
	s.Line = m.Line()
	s.Row = m.Config.Row
	s.Column = m.Config.Column
	s.WildMap = m.Config.WildMap

	// 对所有页面进行随机再生处理
	// for i := range s.Pages {
	// 	s.Pages[i].Grid = m.regenerateGrid(s.Pages[i].Grid, s.Pages[i].Lines, salt)
	// }

	// 最大赔付
	if s.Pays > int32(m.Config.MaxPayout) {
		s.Pays = int32(m.Config.MaxPayout)
	}
	return s
}

func (m400163) Rule(ctx map[string]any) string {
	gs := map[string]any{
		"gamegroup":             "base",
		"doubleAssortment":      []string{"off"},
		"maxBetPerGame_cents":   nil,
		"betAssortment":         ctx["betAssortment"],
		"denomAssortment_cents": []int{1},
		"minBetPerGame_cents":   nil,
		"winValidation": map[string]any{
			"needcheck":                    false,
			"winlimit_fictiveRotate_gcurr": 25000000,
			"remaintime":                   86400000,
			"period":                       86400000,
			"isApproved":                   false,
			"isNotApproved":                false,
			"isWaitApprove":                false,
		},
		"buyBonus": map[string]any{
			"wasBuy":   0,
			"selectId": -1,
			"buyTotalBetK": []map[string]any{
				{"id": 0, "cost": 88, "prefix2": "_BASE_FG", "rtp": 96.25},
				{"id": 1, "cost": 333, "prefix2": "_BASE_FG_HOT", "rtp": 96.31},
				{"id": 2, "cost": 8, "prefix2": "_BASE_WILD", "rtp": 96.21},
			},
		},
		"outRatesVolatility":    nil,
		"placedbet":             ctx["input"],
		"gcurrency":             "",
		"gdenom":                1,
		"present":               "no",
		"betPerGame":            ctx["input"],
		"betPerLine":            ctx["betPerLine"],
		"nlines":                20,
		"phaseCur":              "finished",
		"phaseNext":             "toIdle",
		"maxBetPerGame_credits": 20000000,
		"analInfo": map[string]any{
			"formula": map[string]any{
				"args": []string{"betPerLine", "nlines"},
				"body": "return(betPerLine * nlines/1)",
			},
			"formulaReverse": map[string]any{
				"args": []string{"betPerGame", "nlines"},
				"body": "return(betPerGame / nlines*1)",
			},
			"lineStyles": [][]int{
				{1, 1, 1, 1, 1},
				{0, 0, 0, 0, 0},
				{2, 2, 2, 2, 2},
				{0, 1, 2, 1, 0},
				{2, 1, 0, 1, 2},
				{1, 2, 2, 2, 1},
				{1, 0, 0, 0, 1},
				{0, 1, 1, 1, 0},
				{2, 1, 1, 1, 2},
				{2, 2, 1, 0, 0},
				{0, 0, 1, 2, 2},
				{2, 1, 1, 1, 0},
				{0, 1, 1, 1, 2},
				{1, 1, 0, 1, 1},
				{1, 1, 2, 1, 1},
				{0, 0, 1, 0, 0},
				{2, 2, 1, 2, 2},
				{1, 2, 1, 2, 1},
				{0, 1, 0, 1, 0},
				{2, 1, 2, 1, 2},
				{1, 0, 1, 0, 1},
				{1, 0, 0, 1, 2},
				{1, 2, 2, 1, 0},
				{1, 1, 0, 1, 2},
				{1, 1, 2, 1, 0},
			},
			"symbolNames": []string{
				"granny", "danludan", "horse_arman", "gena", "valya",
				"dog_gandosha", "owl_anfisa", "monstr_red", "monstr_green",
				"monstr_purple", "monstr_blue", "monstr_pink", "scatter",
				"scatter_gold", "granny2", "granny2", "granny3", "granny5",
				"granny10",
			},
			"baseReels": [][]int{
				{9, 9, 1, 7, 7, 6, 6, 8, 8, 5, 5, 5, 11, 11, 3, 3, 3, 10, 6, 6, 6, 9, 9, 9, 2, 11, 11, 1, 7, 7, 2, 8, 8, 8, 12, 9, 9, 9, 3, 10, 10, 10, 4, 8, 8, 1, 1, 1, 9, 9, 12, 10, 10, 10, 3, 3, 8, 8, 5, 5, 7, 7, 12, 8, 8, 5, 7, 7, 12, 9, 9, 2, 2, 2, 10, 6, 10, 4, 4, 4, 4, 11, 11, 5, 5, 5, 11, 11, 4, 10, 10, 10, 1, 11, 11, 11, 13, 7, 7, 7},
				{9, 9, 9, 1, 11, 11, 11, 4, 10, 10, 3, 11, 11, 0, 1, 1, 10, 10, 5, 5, 5, 8, 8, 8, 0, 11, 11, 11, 2, 9, 9, 9, 0, 7, 7, 1, 10, 10, 6, 6, 6, 7, 2, 2, 2, 7, 6, 7, 7, 5, 8, 8, 8, 3, 9, 9, 4, 11, 11, 6, 6, 6, 8, 3, 3, 3, 10, 10, 10, 4, 7, 7, 7, 2, 8, 8, 8, 0, 9, 9, 9, 3, 7, 7, 7, 5, 5, 5, 11, 11, 4, 4, 4, 10, 10, 4, 8, 8, 6, 9},
				{8, 8, 8, 12, 7, 7, 7, 0, 10, 10, 10, 1, 1, 1, 11, 11, 3, 10, 10, 2, 7, 7, 7, 1, 9, 9, 9, 12, 11, 11, 11, 5, 5, 5, 8, 8, 2, 2, 2, 11, 11, 4, 9, 9, 1, 8, 8, 8, 3, 10, 10, 0, 7, 7, 3, 3, 3, 9, 6, 6, 8, 8, 0, 7, 7, 4, 4, 4, 9, 9, 12, 8, 8, 2, 10, 10, 13, 11, 11, 5, 10, 10, 10, 3, 7, 7, 6, 6, 11, 11, 6, 7, 7, 5, 5, 8, 4, 9, 9, 9},
				{10, 10, 10, 0, 7, 7, 4, 8, 8, 1, 9, 9, 9, 6, 10, 10, 5, 11, 11, 3, 3, 3, 10, 10, 2, 9, 5, 5, 9, 9, 4, 4, 4, 11, 11, 11, 5, 5, 8, 8, 0, 9, 9, 3, 10, 10, 4, 11, 5, 7, 7, 7, 0, 8, 8, 2, 2, 2, 9, 6, 6, 6, 11, 11, 11, 1, 9, 9, 9, 0, 10, 10, 2, 8, 8, 8, 4, 7, 7, 5, 5, 5, 9, 1, 1, 1, 11, 11, 11, 0, 10, 10, 3, 8, 8, 3, 7, 7, 5, 11},
				{11, 11, 6, 9, 9, 12, 8, 8, 8, 2, 2, 2, 8, 8, 1, 10, 10, 3, 9, 9, 5, 5, 8, 12, 11, 11, 6, 7, 7, 12, 10, 10, 1, 1, 1, 10, 10, 5, 5, 8, 8, 1, 7, 7, 5, 11, 11, 11, 12, 7, 7, 3, 3, 3, 8, 8, 4, 4, 4, 9, 9, 3, 10, 10, 6, 6, 11, 11, 4, 7, 7, 6, 6, 10, 10, 9, 9, 13, 8, 8, 5, 7, 7, 2, 11, 11, 11, 4, 9, 9, 9, 3, 7, 7, 7, 2, 8, 8, 4, 11},
			},
			"freeReels": [][]int{
				{9, 9, 5, 7, 7, 6, 8, 8, 8, 14, 14, 14, 11, 11, 14, 14, 14, 10, 14, 14, 14, 9, 9, 9, 14, 14, 14, 14, 7, 7, 2, 8, 8, 8, 14, 9, 9, 9, 3, 10, 10, 10, 4, 8, 8, 14, 14, 14, 9, 14, 14, 14, 10, 14, 14, 14, 8, 14, 14, 14, 7, 14, 14, 14, 8, 5, 7, 7, 14, 9, 9, 14, 14, 14, 10, 6, 10, 14, 14, 14, 14, 11, 11, 14, 14, 14, 11, 14, 14, 14, 10, 10, 1, 11, 11, 11, 14, 7, 7, 7},
				{9, 9, 9, 1, 11, 11, 11, 0, 10, 10, 3, 11, 11, 0, 9, 9, 14, 14, 14, 7, 5, 8, 8, 8, 0, 11, 11, 11, 0, 9, 9, 9, 0, 7, 7, 1, 10, 10, 14, 14, 14, 7, 14, 14, 14, 7, 6, 7, 7, 5, 8, 8, 8, 3, 9, 14, 14, 14, 11, 14, 14, 14, 8, 14, 14, 14, 10, 10, 10, 4, 7, 7, 7, 2, 8, 8, 8, 0, 9, 9, 9, 0, 7, 7, 7, 14, 14, 14, 11, 11, 14, 14, 14, 10, 10, 4, 8, 8, 6, 9},
				{8, 8, 14, 14, 14, 7, 7, 0, 10, 10, 10, 14, 14, 14, 11, 14, 14, 14, 10, 14, 14, 14, 7, 14, 14, 14, 9, 0, 11, 11, 11, 14, 14, 14, 8, 8, 14, 14, 14, 11, 11, 4, 9, 9, 1, 8, 8, 8, 3, 10, 10, 0, 7, 7, 14, 14, 14, 9, 14, 14, 14, 8, 0, 7, 7, 14, 14, 14, 9, 9, 0, 8, 8, 2, 10, 10, 0, 11, 11, 5, 10, 5, 5, 0, 7, 7, 6, 6, 11, 11, 6, 7, 7, 5, 5, 8, 4, 9, 9, 9},
				{10, 10, 10, 0, 7, 7, 4, 8, 8, 0, 9, 9, 9, 6, 10, 10, 5, 11, 11, 14, 14, 14, 10, 10, 14, 14, 14, 14, 9, 9, 14, 14, 14, 11, 11, 14, 14, 14, 8, 8, 0, 9, 9, 3, 10, 10, 4, 11, 0, 7, 7, 7, 0, 8, 8, 14, 14, 14, 9, 14, 14, 14, 11, 11, 11, 1, 9, 9, 9, 0, 10, 10, 2, 8, 8, 8, 4, 7, 7, 14, 14, 14, 9, 14, 14, 14, 11, 11, 11, 0, 10, 10, 3, 8, 14, 14, 14, 7, 5, 11},
				{11, 11, 6, 9, 9, 14, 8, 8, 8, 14, 14, 14, 8, 14, 14, 14, 10, 5, 9, 9, 14, 14, 14, 14, 11, 11, 6, 7, 14, 14, 14, 10, 14, 14, 14, 8, 14, 14, 14, 8, 8, 1, 7, 7, 5, 11, 11, 11, 14, 7, 7, 14, 14, 14, 8, 8, 14, 14, 14, 9, 9, 3, 10, 10, 14, 14, 14, 11, 4, 7, 7, 14, 14, 14, 10, 9, 14, 14, 14, 8, 5, 7, 7, 2, 11, 11, 11, 4, 9, 9, 9, 3, 7, 7, 7, 2, 8, 8, 4, 11},
			},
			"statTablo": map[string]interface{}{
				"volatility": 7,
				"bigwin":     7,
				"epicwin":    8,
				"bonus":      8,
				"show":       1,
				"rtp":        96.02,
			},
			"maxWinFreq_big":      7229351,
			"VIP_maxWinFreq_big":  7100173,
			"arrlimits_winLimitK": []int{8888},
			"wildIds":             []int{0, 15, 16, 17, 18},
			"scatterIds":          []int{12, 13},
			"incutIds":            []int{14},
			"minScatters":         []int{3},
			"outRates_vipmode":    96.04,
			"bonusReels": [][]int{
				{9, 9, 1, 7, 7, 6, 6, 8, 8, 5, 5, 5, 11, 11, 3, 3, 3, 10, 6, 6, 6, 9, 9, 9, 2, 11, 11, 1, 7, 7, 2, 8, 8, 8, 2, 9, 9, 9, 3, 10, 10, 10, 4, 8, 8, 1, 1, 1, 9, 9, 3, 10, 10, 10, 3, 3, 8, 8, 5, 5, 7, 7, 4, 8, 8, 5, 7, 7, 9, 9, 2, 2, 2, 10, 6, 10, 4, 4, 4, 4, 11, 11, 5, 5, 5, 11, 11, 11, 4, 10, 10, 10, 1, 11, 11, 11, 1, 7, 7, 7},
				{9, 9, 9, 1, 11, 11, 11, 4, 10, 10, 3, 11, 11, 1, 1, 10, 10, 5, 5, 5, 8, 8, 8, 11, 11, 11, 2, 9, 9, 9, 7, 7, 1, 10, 10, 6, 6, 6, 7, 2, 2, 2, 7, 6, 7, 7, 5, 8, 8, 8, 3, 9, 9, 4, 11, 11, 6, 6, 6, 8, 8, 3, 3, 3, 10, 10, 10, 4, 7, 7, 7, 2, 8, 8, 8, 4, 9, 9, 9, 3, 7, 7, 7, 5, 5, 5, 11, 11, 4, 4, 4, 10, 10, 4, 8, 8, 6, 9, 9, 9},
				{8, 8, 8, 1, 7, 7, 7, 10, 10, 10, 1, 1, 1, 11, 11, 3, 10, 10, 2, 7, 7, 7, 1, 9, 9, 9, 2, 11, 11, 11, 5, 5, 5, 8, 8, 2, 2, 2, 11, 11, 4, 9, 9, 1, 8, 8, 8, 3, 10, 10, 7, 7, 3, 3, 3, 9, 6, 6, 8, 8, 8, 7, 7, 7, 4, 4, 4, 9, 9, 8, 8, 2, 10, 10, 4, 11, 11, 5, 10, 10, 10, 3, 7, 7, 6, 6, 11, 11, 6, 7, 7, 5, 5, 8, 8, 8, 4, 9, 9, 9},
				{10, 10, 10, 7, 7, 4, 8, 8, 1, 9, 9, 9, 6, 10, 10, 5, 11, 11, 3, 3, 3, 10, 10, 2, 9, 5, 5, 9, 9, 4, 4, 4, 11, 11, 11, 5, 5, 8, 8, 9, 9, 3, 10, 10, 4, 11, 5, 7, 7, 7, 8, 8, 2, 2, 2, 9, 6, 6, 6, 11, 11, 11, 1, 9, 9, 9, 3, 10, 10, 10, 2, 8, 8, 8, 4, 7, 7, 5, 5, 5, 9, 1, 1, 1, 11, 11, 11, 10, 10, 10, 3, 8, 8, 3, 7, 7, 5, 11, 11, 11},
				{11, 11, 6, 9, 9, 4, 8, 8, 8, 2, 2, 2, 8, 8, 1, 10, 10, 3, 9, 9, 5, 5, 8, 11, 11, 6, 7, 7, 3, 10, 10, 1, 1, 1, 10, 10, 5, 5, 8, 8, 1, 7, 7, 5, 11, 11, 11, 7, 7, 3, 3, 3, 8, 8, 4, 4, 4, 9, 9, 3, 10, 10, 6, 6, 11, 11, 4, 7, 7, 6, 6, 10, 10, 9, 9, 3, 8, 8, 5, 7, 7, 2, 11, 11, 11, 4, 9, 9, 9, 3, 7, 7, 7, 2, 8, 8, 4, 11, 11, 11},
			},
			"volatility":      4,
			"sasAdditionalId": "CGY",
			"sasPaytableId":   "CGY960",
		},
		"helpInfo": map[string]interface{}{
			"paytable": []interface{}{
				[]interface{}{[]int{0, 1}},
				[]interface{}{[]int{1, 4}, []int{5, 500}, []int{4, 200}, []int{3, 100}},
				[]interface{}{[]int{2, 4}, []int{5, 200}, []int{4, 80}, []int{3, 40}},
				[]interface{}{[]int{3, 4}, []int{5, 150}, []int{4, 60}, []int{3, 30}},
				[]interface{}{[]int{4, 4}, []int{5, 100}, []int{4, 40}, []int{3, 20}},
				[]interface{}{[]int{5, 4}, []int{5, 80}, []int{4, 30}, []int{3, 15}},
				[]interface{}{[]int{6, 4}, []int{5, 60}, []int{4, 20}, []int{3, 10}},
				[]interface{}{[]int{7, 4}, []int{5, 40}, []int{4, 15}, []int{3, 8}},
				[]interface{}{[]int{8, 4}, []int{5, 35}, []int{4, 14}, []int{3, 7}},
				[]interface{}{[]int{9, 4}, []int{5, 30}, []int{4, 12}, []int{3, 6}},
				[]interface{}{[]int{10, 4}, []int{5, 25}, []int{4, 8}, []int{3, 4}},
				[]interface{}{[]int{11, 4}, []int{5, 20}, []int{4, 5}, []int{3, 2}},
				[]interface{}{[]int{12, 8}},
				[]interface{}{[]int{13, 8}},
				[]interface{}{[]int{14, 16}},
				[]interface{}{[]int{15, 1}},
				[]interface{}{[]int{16, 1}},
				[]interface{}{[]int{17, 1}},
				[]interface{}{[]int{18, 1}},
			},
			"fg": map[string]interface{}{
				"minAward": 7,
				"maxAward": 20,
				"limit":    20,
			},
			"doubles": []interface{}{[]interface{}{"off", 0, 0}},
		},
		"doubleActive":           "off",
		"doubleActiveDbSettings": "off",
		"antiDynamiteBet":        nil,
		"dramshow":               nil,
		"versions": map[string]interface{}{
			"server_core": "1.1",
			"server_game": "1.0",
			"server_math": "1.0",
		},
		"winlimits": map[string]interface{}{
			"maxWinLimitK":         8888,
			"maxWin_gcurr":         nil,
			"needControlJackpot":   false,
			"winLimitK_gameconfig": 8888,
		},
		"isMaxFlag":       0,
		"isMaxFlag_lines": 0,
		"linesAssortment": []int{20},
		"linesPerCredit":  1,
		"reelstate":       0,
		"aux":             0,
		"startBox":        [][]int{{1, 5, 10, 1, 9}, {11, 8, 10, 9, 9}, {11, 8, 1, 9, 3}},
		"stopBox":         [][]int{{1, 5, 10, 1, 9}, {11, 8, 10, 9, 9}, {11, 8, 1, 9, 3}},
		"incutId":         1,
		"vipMode": map[string]interface{}{
			"on":             0,
			"vipBetK":        1.25,
			"wasBuyVip":      0,
			"vip_noSpecSeed": false,
		},
		"setVip_inFreeSpinAlways": -1,
		"bbLimitsWinK":            []int{8888, 8888, 4500},
		"helpseed":                true,
		"shotsList":               []interface{}{},
	}

	b, _ := json.Marshal(gs)
	return string(b)
}

func (m400163) InputCoef(ctl int32) int32 {
	switch ctl {
	case 1:
		return 8800
	case 2:
		return 33300
	case 3:
		return 800
	case 4:
		return 125
	default:
		return 100
	}
}

func cloneWilds(src map[int]int16) map[int]int16 {
	dst := make(map[int]int16, len(src))
	for k, v := range src {
		dst[k] = v
	}
	return dst
}

func (m *m400163) isSpecialIcon(icon int16) bool {
	// Scatter 图标和 Wild 图标
	if icon >= 12 {
		return true
	}

	return false
}

// regenerateGrid 实现棋盘随机再生功能
func (m *m400163) regenerateGrid(originalGrid []int16, lines []belatra.LinesInfo, salt *rand.Rand) []int16 {
	if len(lines) == 0 {
		// 没有中奖线，直接返回原棋盘
		return originalGrid
	}

	// 创建新的棋盘副本
	newGrid := make([]int16, len(originalGrid))
	copy(newGrid, originalGrid)

	// 获取所有中奖位置
	winningPositions := m.getWinningPositions(originalGrid, lines)

	// 获取非中奖位置的图标
	nonWinningIcons := m.getNonWinningIcons(originalGrid, winningPositions)

	// 如果没有非中奖图标，直接返回原棋盘
	if len(nonWinningIcons) == 0 {
		return originalGrid
	}

	// 使用 Fisher-Yates 算法打乱非中奖图标
	utils.ShuffleIcons(nonWinningIcons, salt)

	// 将打乱后的图标重新放置到非中奖位置
	iconIndex := 0
	for i := 0; i < len(newGrid); i++ {
		if !winningPositions[i] {
			if iconIndex < len(nonWinningIcons) {
				newGrid[i] = nonWinningIcons[iconIndex]
				iconIndex++
			}
		}
	}

	// 验证重排后不会产生新的中奖组合
	if m.hasNewWinningLines(newGrid, originalGrid, lines) {
		// 如果产生了新的中奖线，返回原棋盘
		return originalGrid
	}

	return newGrid
}

// getWinningPositions 获取所有中奖位置
func (m *m400163) getWinningPositions(grid []int16, lines []belatra.LinesInfo) map[int]bool {
	winningPositions := make(map[int]bool)

	for _, line := range lines {
		// 根据连线ID获取对应的连线模式
		if line.ID < len(m.Config.Pattern) {
			pattern := m.Config.Pattern[line.ID]

			// 获取该连线的符号
			symbols := make([]int16, len(pattern))
			for i, pos := range pattern {
				row := pos.Row()
				col := pos.Column()
				if row >= 0 && row < int32(m.Config.Row) && col >= 0 && col < int32(m.Config.Column) {
					index := row*int32(m.Config.Column) + col
					symbols[i] = grid[index]
				}
			}

			// 检查连线并标记中奖位置
			if ok, _, _, _ := m.checkLine(symbols); ok {
				// 标记中奖位置
				for i, pos := range pattern {
					row := pos.Row()
					col := pos.Column()
					if row >= 0 && row < int32(m.Config.Row) && col >= 0 && col < int32(m.Config.Column) {
						index := row*int32(m.Config.Column) + col
						// 只标记实际参与中奖的位置
						if i < len(line.Iwin.OnLine) && line.Iwin.OnLine[i] != 127 {
							winningPositions[int(index)] = true
						}
					}
				}
			}
		}
	}

	return winningPositions
}

// getNonWinningIcons 获取非中奖位置的图标并排除特殊符号
// 排除 wild 图标 (>= 14) 和 scatter 图标，确保特殊图标不被随机打乱
func (m *m400163) getNonWinningIcons(grid []int16, winningPositions map[int]bool) []int16 {
	var nonWinningIcons []int16

	for i := 0; i < len(grid); i++ {
		// 跳过中奖位置
		if winningPositions[i] {
			continue
		}

		icon := grid[i]

		if m.isSpecialIcon(icon) {
			continue
		}

		// 只有普通图标才加入随机打乱列表
		nonWinningIcons = append(nonWinningIcons, icon)
	}

	return nonWinningIcons
}

// hasNewWinningLines 检查重排后是否产生了新的中奖线
func (m *m400163) hasNewWinningLines(newGrid, originalGrid []int16, originalLines []belatra.LinesInfo) bool {
	// 计算新棋盘的中奖线
	wilds := make(map[int]int16)
	newWilds := m.parseWilds(newGrid, wilds)

	// 创建一个临时的 S400163 结构体用于计算赔付
	tempSpin := &games.S400163{}
	newLines := m.parsePayout(newGrid, newWilds, tempSpin)

	// 如果新的中奖线数量不同，说明产生了新的中奖组合
	if len(newLines) != len(originalLines) {
		return true
	}

	// 检查每条中奖线的赔付是否相同
	originalPayouts := make(map[int]int64)
	for _, line := range originalLines {
		originalPayouts[line.ID] = line.Iwin.Win
	}

	for _, line := range newLines {
		if originalPayout, exists := originalPayouts[line.ID]; !exists || originalPayout != line.Iwin.Win {
			return true
		}
	}

	return false
}

// 不同基础玩法最小赔率
func (m m400163) MinPayout(ctl int32) int32 {
	mode, ok := m.Config.MinLimit[ctl]
	if !ok {
		return 0
	}
	return mode.X * m.Line()
}
