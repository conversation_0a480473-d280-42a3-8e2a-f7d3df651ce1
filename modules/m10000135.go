package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*m10000135])

type c10000135 struct {
	MaxPayout   int32              // 最大派彩
	Pattern     [][]basic.Position // 连线模式
	Row         int                // 行数
	Column      int                // 列数
	WildIcon    int16              // 百搭图标
	ScatterIcon int16              // Scatter图标
	PayoutTable map[int16][]int16  // 赔付表
	IconWeight  map[int16]int      // 图标权重
	BaseCoef    []int              // 基础倍数(普通模式递增)
	FreeCoef    []map[int]struct { // 免费游戏配置
		FreeTimes int   // 免费次数
		BeiLv     []int // 倍率
	}
	FreeCoefWeight map[int16]int
	FreeSpin       struct { // 免费游戏触发条件
		Icon     int16 // 触发图标
		MinCount int   // 最小数量
	}
}

type m10000135 struct {
	Config               c10000135
	RandByWeight         *utils.RandomWeightPicker[int16, int]
	RandByWeightNoWild   *utils.RandomWeightPicker[int16, int]
	RandByFreeCoefWeight *utils.RandomWeightPicker[int16, int]
}

func (m *m10000135) Init(config []byte) {
	m.Config = utils.ParseYAML[c10000135](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)

	// 创建临时权重表，第一列和第五列不包含 wild 图标
	tempWeight := make(map[int16]int)
	for k, v := range m.Config.IconWeight {
		if k != m.Config.WildIcon {
			tempWeight[k] = v
		}
	}

	// 创建随机选择器
	m.RandByWeightNoWild = utils.NewRandomWeightPicker(tempWeight)
	m.RandByFreeCoefWeight = utils.NewRandomWeightPicker(m.Config.FreeCoefWeight)
}

func (m m10000135) ID() int32 {
	return 10000135
}

func (m m10000135) Line() int32 { // 线数
	return 20
}

func (m m10000135) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m10000135) Exception(code int32) string {
	return games.S10000135{}.Exception(code)
}

func (m *m10000135) Spin(rd *rand.Rand) basic.ISpin {
	// 生成初始网格 - 按照列优先排列
	grid := make([]int16, m.Config.Row*m.Config.Column)
	for col := 0; col < m.Config.Column; col++ {
		for row := 0; row < m.Config.Row; row++ {
			index := col*m.Config.Row + row
			if col == 0 || col == 4 { // 第一列或第五列
				grid[index] = m.RandByWeightNoWild.One(rd)
			} else {
				grid[index] = m.RandByWeight.One(rd)
			}
		}
	}

	// 创建结果
	spin := &games.S10000135{
		Pages:  []*games.P10000135{},
		Rounds: []*games.S10000135{},
		Row:    m.Config.Row,
		Column: m.Config.Column,
		IsFree: false,
	}

	_, freeSpins := m.generatePage(grid, spin, rd)

	// 普通模式也存入Rounds中
	spin.Rounds = append(spin.Rounds, spin)

	// 如果触发了免费模式
	if freeSpins > 0 {
		spin.IsFree = true
		spin.FreeSpins = freeSpins
		// 免费旋转模式选择：
		// 模式1：固定次数，不累加（当前启用）
		// 模式2：动态累加，在免费旋转中再次触发scatter时增加免费次数（已注释）

		remainingFreeSpins := freeSpins

		for {
			// 生成新的网格
			freeGrid := make([]int16, m.Config.Row*m.Config.Column)
			for col := 0; col < m.Config.Column; col++ {
				for row := 0; row < m.Config.Row; row++ {
					index := col*m.Config.Row + row
					if col == 0 || col == 4 { // 第一列或第五列
						freeGrid[index] = m.RandByWeightNoWild.One(rd)
					} else {
						freeGrid[index] = m.RandByWeight.One(rd)
					}
				}
			}

			// 创建免费旋转轮次
			freeRound := &games.S10000135{
				Pages:     []*games.P10000135{},
				IsFree:    true,
				Row:       m.Config.Row,
				Column:    m.Config.Column,
				FreeIndex: spin.FreeIndex,
				BeiLv:     spin.BeiLv,
			}

			_, additionalFreeSpins := m.generatePage(freeGrid, freeRound, rd)
			// 完成一次转盘后进行判断，如果触发了新的免费次数则累加
			if additionalFreeSpins > 0 {
				remainingFreeSpins += additionalFreeSpins
			}

			spin.Pays += freeRound.Pays
			spin.Rounds = append(spin.Rounds, freeRound)

			// 注释：动态累加循环的结束逻辑，如需启用可取消注释
			// 减少剩余免费次数
			remainingFreeSpins--

			if remainingFreeSpins == 0 {
				break

			} // 动态累加循环结束
		}

	}

	return spin
}

func (m *m10000135) generatePage(grid []int16, spin *games.S10000135, rd *rand.Rand) (*games.S10000135, int32) {
	// 进行级联处理
	currentGrid := grid
	totalPayout := int32(0)
	cascadeCount := 0
	var freeSpins int32 = 0

	for {
		// 检查 Scatter 图标
		if m.Config.FreeSpin.Icon > 0 {
			scatterCount := 0
			for _, icon := range currentGrid {
				if icon == m.Config.FreeSpin.Icon {
					scatterCount++
				}
			}
			if scatterCount >= m.Config.FreeSpin.MinCount {
				// 如果还没有设置FreeIndex（第一次触发），则随机选择一个
				if spin.FreeIndex == 0 && !spin.IsFree {
					spin.FreeIndex = (m.RandByFreeCoefWeight.One(rd))
				}
				// 获取免费次数
				for _, freeConfig := range m.Config.FreeCoef[spin.FreeIndex] {
					freeSpins = int32(freeConfig.FreeTimes)
					spin.BeiLv = freeConfig.BeiLv
					break
				}
			} else {
				freeSpins = 0
			}
		}

		// 解析当前网格的连线赔付
		lines := m.parsePayout(currentGrid)

		// 计算当前页的基础赔付
		var pagePayout int32
		for _, line := range lines {
			pagePayout += line.Value
		}

		// 应用级联倍率
		var multiplier int32 = 1
		if cascadeCount > 0 {
			if spin.IsFree && len(spin.BeiLv) > 0 && cascadeCount-1 < len(spin.BeiLv) {
				multiplier = int32(spin.BeiLv[cascadeCount-1])
			} else if !spin.IsFree && cascadeCount <= len(m.Config.BaseCoef) {
				multiplier = int32(m.Config.BaseCoef[cascadeCount-1])
			}
		}

		// 保存当前网格用于 aftert
		nextGrid := make([]int16, len(currentGrid))
		copy(nextGrid, currentGrid)

		// 添加当前页
		page := &games.P10000135{
			Grid:       currentGrid,
			Pay:        pagePayout,
			Lines:      lines,
			Multiplier: multiplier,
			FreeSpins:  freeSpins,
		}

		spin.Pages = append(spin.Pages, page)

		// 累计总赔付
		totalPayout += pagePayout

		// 如果没有中奖线，结束级联
		if len(lines) == 0 {
			break
		}

		// 执行级联，移除中奖图标并生成新图标
		newGrid := make([]int16, len(currentGrid))
		copy(newGrid, currentGrid)

		// 收集中奖位置
		winPositions := make(map[int]bool)
		for _, line := range lines {
			for _, pos := range line.Positions {
				winPositions[pos] = true
			}
		}

		// 对每一列进行处理 - 按照列优先的方式
		for col := 0; col < m.Config.Column; col++ {
			colWinPositions := []int{}
			for pos := range winPositions {
				// 将位置转换为列优先索引
				colIndex := pos / m.Config.Row
				if colIndex == col {
					colWinPositions = append(colWinPositions, pos)
				}
			}

			if len(colWinPositions) == 0 {
				continue
			}

			nonWinIcons := []int16{}
			for row := 0; row < m.Config.Row; row++ {
				pos := col*m.Config.Row + row
				if !winPositions[pos] {
					nonWinIcons = append(nonWinIcons, currentGrid[pos])
				}
			}

			newIcons := []int16{}
			for i := 0; i < len(colWinPositions); i++ {
				if col == 0 || col == 4 {
					newIcons = append(newIcons, m.RandByWeightNoWild.One(rd))
				} else {
					newIcons = append(newIcons, m.RandByWeight.One(rd))
				}
			}

			for row := m.Config.Row - 1; row >= 0; row-- {
				pos := col*m.Config.Row + row
				if len(nonWinIcons) > 0 {
					newGrid[pos] = nonWinIcons[len(nonWinIcons)-1]
					nonWinIcons = nonWinIcons[:len(nonWinIcons)-1]
				} else {
					newGrid[pos] = newIcons[len(newIcons)-1]
					newIcons = newIcons[:len(newIcons)-1]
				}
			}
		}

		// 保留 Scatter 图标在原位置
		for i := 0; i < len(currentGrid); i++ {
			if currentGrid[i] == m.Config.ScatterIcon {
				newGrid[i] = m.Config.ScatterIcon
			}
		}

		currentGrid = newGrid
		cascadeCount++
	}

	spin.Pays = totalPayout
	spin.FreeSpins = freeSpins

	if spin.Pays > m.Config.MaxPayout {
		spin.Pays = m.Config.MaxPayout
	}
	return spin, freeSpins
}

func (m m10000135) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rand.New(rd))
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

func (m *m10000135) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	r := spin.(*games.S10000135)
	r.GameId = m.ID()
	r.Line = m.Line()
	r.Row = m.Config.Row
	r.Column = m.Config.Column
	return r
}

func (m m10000135) parsePayout(grid []int16) []games.L10000135 {
	// 将grid转换为按列排列的二维数组，方便访问
	gridCols := make([][]int16, m.Config.Column)
	for col := 0; col < m.Config.Column; col++ {
		gridCols[col] = make([]int16, m.Config.Row)
		for row := 0; row < m.Config.Row; row++ {
			gridCols[col][row] = grid[col*m.Config.Row+row]
		}
	}

	// 存储所有符合条件的连线
	var lines []games.L10000135

	// 遍历所有连线模式
	for lineID, pattern := range m.Config.Pattern {
		// 获取第一个位置的图标
		firstPos := pattern[0]
		col := int(firstPos.Column())
		row := int(firstPos.Row())

		// 确保位置在有效范围内
		if col >= 0 && col < m.Config.Column && row >= 0 && row < m.Config.Row {
			icon := gridCols[col][row]

			// 如果是百搭图标或scatter图标，无法作为起始图标
			if icon == m.Config.WildIcon || icon == m.Config.ScatterIcon {
				continue
			}

			// 计算连续匹配的数量
			count := 1
			positions := []int{col*m.Config.Row + row}

			// 检查后续位置
			for i := 1; i < len(pattern); i++ {
				pos := pattern[i]
				c := int(pos.Column())
				r := int(pos.Row())

				// 确保位置在有效范围内
				if c < 0 || c >= m.Config.Column || r < 0 || r >= m.Config.Row {
					break
				}

				// 获取当前位置的图标
				currentIcon := gridCols[c][r]

				// 如果当前图标与起始图标相同或者是百搭，则匹配成功
				// Scatter图标不参与连线
				if currentIcon == icon || (currentIcon == m.Config.WildIcon && currentIcon != m.Config.ScatterIcon) {
					count++
					positions = append(positions, c*m.Config.Row+r)
				} else {
					break
				}
			}

			// 如果连线长度至少为3，计算赔付
			if count >= 3 {
				// 获取该图标对应的赔付值
				payoutValues, exists := m.Config.PayoutTable[icon]
				if !exists {
					continue
				}

				// 确保索引在有效范围内
				if count-1 < len(payoutValues) {
					payout := payoutValues[count-1]

					// 创建连线记录
					line := games.L10000135{
						ID:        lineID + 1, // 连线ID从1开始
						Positions: positions,
						Icon:      icon,
						Count:     count,
						Value:     int32(payout),
					}

					lines = append(lines, line)
				}
			}
		}
	}

	return lines
}

func (m m10000135) Rule(ctx map[string]any) string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

func (m10000135) InputCoef(int32) int32 {
	return 100
}

func (m m10000135) MinPayout(ctl int32) int32 {
	return 0
}
