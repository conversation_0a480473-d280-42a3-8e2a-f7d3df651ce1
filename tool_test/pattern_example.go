package test

import (
	"fmt"
	"log"
)

// ExampleUsage 展示如何使用PatternConverter
func ExampleUsage() {
	// 示例line数据（来自测试文件）
	lineJSON := `[
    [1, 1, 1, 1, 1],
    [0, 0, 0, 0, 0],
    [2, 2, 2, 2, 2],
    [0, 1, 2, 1, 0],
    [2, 1, 0, 1, 2],
    [1, 2, 2, 2, 1],
    [1, 0, 0, 0, 1],
    [0, 1, 1, 1, 0],
    [2, 1, 1, 1, 2],
    [2, 2, 1, 0, 0],
    [0, 0, 1, 2, 2],
    [2, 1, 1, 1, 0],
    [0, 1, 1, 1, 2],
    [1, 1, 0, 1, 1],
    [1, 1, 2, 1, 1],
    [0, 0, 1, 0, 0],
    [2, 2, 1, 2, 2],
    [1, 2, 1, 2, 1],
    [0, 1, 0, 1, 0],
    [2, 1, 2, 1, 2],
    [1, 0, 1, 0, 1],
    [1, 0, 0, 1, 2],
    [1, 2, 2, 1, 0],
    [1, 1, 0, 1, 2],
    [1, 1, 2, 1, 0]
	]`

	// 创建转换器
	converter := NewPatternConverter()

	// 显示格式说明
	fmt.Println("=== Pattern格式说明 ===")
	fmt.Println(converter.GetPatternExplanation())

	// 方法1: 基于数据值的转换
	fmt.Println("=== 方法1: 基于数据值的转换 ===")
	result1, err := converter.ConvertLineToPattern(lineJSON, 3)
	if err != nil {
		log.Fatalf("转换失败: %v", err)
	}
	fmt.Println(result1)

	// 方法2: 标准连线模式转换
	fmt.Println("=== 方法2: 标准连线模式转换 ===")
	// 先解析数据
	var lineData [][]int
	// 这里简化处理，实际使用中应该解析JSON
	lineData = [][]int{
		{1, 1, 1, 1, 1}, {0, 0, 0, 0, 0}, {2, 2, 2, 2, 2},
		{0, 1, 2, 1, 0}, {2, 1, 0, 1, 2}, {1, 2, 2, 2, 1},
		{1, 0, 0, 0, 1}, {0, 1, 1, 1, 0}, {2, 1, 1, 1, 2},
		{2, 2, 1, 0, 0}, {0, 0, 1, 2, 2}, {2, 1, 1, 1, 0},
		{0, 1, 1, 1, 2}, {1, 1, 0, 1, 1}, {1, 1, 2, 1, 1},
		{0, 0, 1, 0, 0}, {2, 2, 1, 2, 2}, {1, 2, 1, 2, 1},
		{0, 1, 0, 1, 0}, {2, 1, 2, 1, 2}, {1, 0, 1, 0, 1},
		{1, 0, 0, 1, 2}, {1, 2, 2, 1, 0}, {1, 1, 0, 1, 2},
		{1, 1, 2, 1, 0},
	}

	result2 := converter.ConvertToStandardPattern(lineData)
	fmt.Println(result2)

	fmt.Println("=== 转换完成 ===")
	fmt.Println("现在你可以将上述Pattern配置复制到 bin/configs/x.yaml文件中使用")
}

// ConvertCustomLine 转换自定义的line数据
func ConvertCustomLine(customLineJSON string) {
	converter := NewPatternConverter()

	result, err := converter.ConvertLineToPattern(customLineJSON, 3)
	if err != nil {
		log.Printf("转换失败: %v", err)
		return
	}

	fmt.Println("=== 自定义数据转换结果 ===")
	fmt.Println(result)
}
