package test

import (
	"encoding/json"
	"fmt"
	"testing"
)

func TestPattern(t *testing.T) {
	line := `[
                [2, 2, 2, 2, 2],
                [1, 1, 1, 1, 1],
                [3, 3, 3, 3, 3],
                [0, 0, 0, 0, 0],
                [0, 1, 2, 1, 0],
                [3, 2, 1, 2, 3],
                [1, 2, 3, 2, 1],
                [2, 1, 0, 1, 2],
                [1, 1, 0, 1, 1],
                [2, 2, 3, 2, 2],
                [0, 0, 1, 0, 0],
                [3, 3, 2, 3, 3],
                [0, 1, 0, 1, 0],
                [3, 2, 3, 2, 3],
                [1, 0, 1, 0, 1],
                [2, 3, 2, 3, 2],
                [0, 1, 1, 1, 0],
                [3, 2, 2, 2, 3],
                [2, 3, 3, 3, 2],
                [1, 0, 0, 0, 1],
                [2, 1, 2, 1, 2],
                [1, 2, 1, 2, 1],
                [1, 2, 2, 2, 1],
                [1, 1, 2, 1, 1],
                [2, 2, 1, 2, 2]
            ]`

	// 解析 JSON 数据
	var lineData [][]int
	err := json.Unmarshal([]byte(line), &lineData)
	if err != nil {
		t.Fatalf("解析 JSON 失败: %v", err)
	}

	// 方法1: 基于数据值的转换
	fmt.Println("=== 方法1: 基于数据值的转换 ===")
	patternOutput1 := convertToPatternFormat(lineData, 4)
	fmt.Println(patternOutput1)

	// 方法2: 直接映射转换（推荐）
	fmt.Println("=== 方法3: 直接映射转换（推荐使用）===")
	patternOutput3 := convertToDirectMappingFormat(lineData)
	fmt.Println(patternOutput3)

	// // 使用自定义转换器
	// fmt.Println("=== 使用PatternConverter工具 ===")
	// converter := NewPatternConverter()
	// converterResult, err := converter.ConvertLineToPattern(line)
	// if err != nil {
	// 	t.Fatalf("转换器转换失败: %v", err)
	// }
	// fmt.Println(converterResult)
}

// convertToPatternFormat 将 line 数据转换为符合 @400163.yaml 中 pattern 字段格式的字符串
func convertToPatternFormat(lineData [][]int, rowNum int) string {
	var result string
	result += "Pattern:\n"

	// 根据 400163.yaml 的标准 Pattern 格式，每行代表一个连线模式
	// 位置编码格式：第一位数字表示行（1=第0行，2=第1行，3=第2行）
	// 第二位数字表示列（1=第0列，2=第1列，3=第2列，4=第3列，5=第4列）

	for i, row := range lineData {
		result += fmt.Sprintf("  - [")
		for j, val := range row {
			// 根据数据值确定在3x5网格中的位置
			// val=0 -> 第1行, val=1 -> 第2行, val=2 -> 第3行
			rowPos := (val % rowNum) + 1 // 将0,1,2映射到1,2,3
			colPos := j + 1              // 列位置 (1-5)
			position := rowPos*10 + colPos

			if j > 0 {
				result += ", "
			}
			result += fmt.Sprintf("%d", position)
		}
		result += fmt.Sprintf("] # %d\n", i+1)
	}

	return result
}

// convertToDirectMappingFormat 直接将数据映射为连线位置（推荐方法）
func convertToDirectMappingFormat(lineData [][]int) string {
	var result string
	result += "Pattern:\n"

	for i, row := range lineData {
		result += fmt.Sprintf("  - [")
		for j, val := range row {
			// 直接将数据值映射为位置编码
			// 这里假设：
			// - 数据中的值0,1,2代表不同的行位置偏好
			// - 但最终还是要生成标准的3x5网格位置编码

			// 根据列位置和数据值生成位置编码
			var rowPos int
			rowPos = val + 1
			// switch val {
			// case 0:
			// 	rowPos = 1 // 第1行
			// case 1:
			// 	rowPos = 2 // 第2行
			// case 2:
			// 	rowPos = 3 // 第3行
			// case 3:
			// 	rowPos = 4 // 第4行
			// case 4:
			// 	rowPos = 5 // 第5行
			// case 5:
			// 	rowPos = 6 // 第6行
			// case 6:
			// 	rowPos = 7 // 第7行
			// 	// 可以根据需要添加更多行
			// default:
			// 	rowPos = 2 // 默认第2行
			// }

			colPos := j + 1 // 列位置 (1-5)
			position := rowPos*10 + colPos

			if j > 0 {
				result += ", "
			}
			result += fmt.Sprintf("%d", position)
		}
		result += fmt.Sprintf("] # %d\n", i+1)
	}

	return result
}
