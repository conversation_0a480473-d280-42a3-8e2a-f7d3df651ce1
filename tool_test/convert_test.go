package test

import (
	"encoding/json"
	"fmt"
	"reflect"
	"sort"
	"strings"
	"testing"
)

// JSONToGoMapString 将JSON字符串转换为Go语言map[string]any{}格式的字符串
func JSONToGoMapString(jsonStr string) (string, error) {
	var data any
	if err := json.Unmarshal([]byte(jsonStr), &data); err != nil {
		return "", fmt.Errorf("解析JSON失败: %v", err)
	}

	return convertToGoMapString(data, 0), nil
}

// convertToGoMapString 递归转换任意类型为Go map字符串格式
func convertToGoMapString(data any, indent int) string {
	if data == nil {
		return "nil"
	}

	v := reflect.ValueOf(data)
	switch v.Kind() {
	case reflect.Map:
		return convertMapToGoString(data.(map[string]any), indent)
	case reflect.Slice:
		return convertSliceToGoString(data.([]any), indent)
	case reflect.String:
		return fmt.Sprintf(`"%s"`, data.(string))
	case reflect.Bool:
		return fmt.Sprintf("%t", data.(bool))
	case reflect.Float64:
		// JSON数字默认解析为float64
		f := data.(float64)
		if f == float64(int64(f)) {
			return fmt.Sprintf("%d", int64(f))
		}
		return fmt.Sprintf("%g", f)
	default:
		return fmt.Sprintf("%v", data)
	}
}

// convertMapToGoString 转换map为Go字符串格式
func convertMapToGoString(m map[string]any, indent int) string {
	if len(m) == 0 {
		return "map[string]any{}"
	}

	var builder strings.Builder
	builder.WriteString("map[string]any{\n")

	// 对键进行排序以保证输出一致性
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	for _, key := range keys {
		value := m[key]
		indentStr := strings.Repeat("\t", indent+1)
		builder.WriteString(fmt.Sprintf(`%s"%s": %s,`+"\n", indentStr, key, convertToGoMapString(value, indent+1)))
	}

	builder.WriteString(strings.Repeat("\t", indent) + "}")
	return builder.String()
}

// convertSliceToGoString 转换slice为Go字符串格式
func convertSliceToGoString(s []any, indent int) string {
	if len(s) == 0 {
		return "[]any{}"
	}

	// 检查slice中所有元素的类型以确定最合适的类型
	sliceType := determineSliceType(s)

	var builder strings.Builder
	builder.WriteString(sliceType + "{")

	// 如果是简单类型且元素不多，可以放在一行
	if isSimpleSlice(s) && len(s) <= 10 {
		for i, item := range s {
			if i > 0 {
				builder.WriteString(", ")
			}
			builder.WriteString(convertToGoMapString(item, indent))
		}
	} else {
		// 复杂类型或元素较多，分行显示
		// builder.WriteString("\n")
		for _, item := range s {
			// indentStr := strings.Repeat("\t", indent+1)
			builder.WriteString(fmt.Sprintf("%s%s,", "", convertToGoMapString(item, indent+1)))
		}
		// builder.WriteString(strings.Repeat("\t", indent))
	}

	builder.WriteString("}")
	return builder.String()
}

// determineSliceType 确定slice的Go类型
func determineSliceType(s []any) string {
	if len(s) == 0 {
		return "[]any"
	}

	// 检查是否所有元素都是同一类型
	firstType := reflect.TypeOf(s[0])
	allSameType := true

	for _, item := range s {
		if reflect.TypeOf(item) != firstType {
			allSameType = false
			break
		}
	}

	if !allSameType {
		return "[]any"
	}

	// 根据第一个元素的类型确定slice类型
	switch s[0].(type) {
	case string:
		return "[]string"
	case bool:
		return "[]bool"
	case float64:
		// 检查是否都是整数
		allInts := true
		for _, item := range s {
			if f, ok := item.(float64); ok {
				if f != float64(int64(f)) {
					allInts = false
					break
				}
			}
		}
		if allInts {
			return "[]int"
		}
		return "[]float64"
	case map[string]any:
		return "[]map[string]any"
	default:
		return "[]any"
	}
}

// isSimpleSlice 判断是否为简单类型的slice
func isSimpleSlice(s []any) bool {
	for _, item := range s {
		switch item.(type) {
		case map[string]any, []any:
			return false
		}
	}
	return true
}

// TestJSONToGoMapString 测试JSON转换为Go map字符串的功能
func TestJSONToGoMapString(t *testing.T) {
	// 测试用例1：简单对象
	jsonStr1 := `{
        "gamegroup": "base",
        "doubleAssortment": ["off"],
        "maxBetPerGame_cents": null,
        "betAssortment": [1, 2, 3, 4, 5, 6, 8, 12, 16, 20, 40, 60, 80],
        "denomAssortment_cents": [1],
        "minBetPerGame_cents": null,
        "winValidation": {
            "needcheck": false,
            "winlimit_fictiveRotate_gcurr": 25000000,
            "remaintime": 86400000,
            "period": 86400000,
            "isApproved": false,
            "isNotApproved": false,
            "isWaitApprove": false
        },
        "outRatesVolatility": null,
        "placedbet": 50,
        "gcurrency": "FUN",
        "gdenom": 1,
        "present": "no",
        "betPerGame": 25,
        "betPerLine": 1,
        "nlines": 25,
        "phaseCur": "finished",
        "phaseNext": "toIdle",
        "maxBetPerGame_credits": 2000,
        "analInfo": {
            "formula": {
                "args": ["betPerLine", "nlines"],
                "body": "return(betPerLine * nlines/1)"
            },
            "formulaReverse": {
                "args": ["betPerGame", "nlines"],
                "body": "return(betPerGame / nlines*1)"
            },
            "lineStyles": [
                [2, 2, 2, 2, 2],
                [1, 1, 1, 1, 1],
                [3, 3, 3, 3, 3],
                [0, 0, 0, 0, 0],
                [0, 1, 2, 1, 0],
                [3, 2, 1, 2, 3],
                [1, 2, 3, 2, 1],
                [2, 1, 0, 1, 2],
                [1, 1, 0, 1, 1],
                [2, 2, 3, 2, 2],
                [0, 0, 1, 0, 0],
                [3, 3, 2, 3, 3],
                [0, 1, 0, 1, 0],
                [3, 2, 3, 2, 3],
                [1, 0, 1, 0, 1],
                [2, 3, 2, 3, 2],
                [0, 1, 1, 1, 0],
                [3, 2, 2, 2, 3],
                [2, 3, 3, 3, 2],
                [1, 0, 0, 0, 1],
                [2, 1, 2, 1, 2],
                [1, 2, 1, 2, 1],
                [1, 2, 2, 2, 1],
                [1, 1, 2, 1, 1],
                [2, 2, 1, 2, 2]
            ],
            "symbolNames": [
                "wolf",
                "bear",
                "deer",
                "puma",
                "eagle",
                "symb_a",
                "symb_k",
                "symb_q",
                "symb_j",
                "scatter",
                "moon"
            ],
            "baseReels": [
                [
                    8, 8, 8, 9, 7, 7, 7, 1, 5, 5, 10, 10, 4, 4, 6, 6, 6, 6, 1,
                    7, 7, 7, 2, 2, 5, 5, 5, 3, 3, 0, 0, 2, 5, 5, 5, 5, 1, 1, 7,
                    7, 4, 4, 6, 6, 6, 3, 8, 8, 8, 9, 7, 7, 7, 1, 5, 5, 4, 4, 6,
                    6, 6, 6, 1, 7, 7, 7, 2, 2, 5, 5, 5, 3, 3, 0, 0, 2, 5, 5, 5,
                    5, 1, 1, 7, 7, 4, 4, 6, 6, 6, 3
                ],
                [
                    7, 7, 3, 10, 10, 8, 8, 8, 8, 1, 1, 5, 5, 10, 10, 5, 4, 4, 8,
                    8, 8, 9, 5, 5, 5, 3, 0, 0, 10, 10, 5, 5, 5, 9, 4, 8, 8, 8,
                    2, 5, 5, 5, 1, 1, 10, 10, 6, 6, 6, 4, 4, 7, 7, 3, 8, 8, 8,
                    8, 1, 1, 5, 5, 5, 4, 4, 8, 10, 10, 8, 8, 9, 5, 5, 5, 3, 0,
                    0, 5, 5, 5, 9, 4, 8, 8, 8, 2, 10, 10, 5, 5, 5, 1, 1, 6, 6,
                    6, 4, 4
                ],
                [
                    4, 8, 8, 8, 8, 10, 10, 2, 2, 7, 7, 7, 7, 3, 3, 10, 10, 8, 8,
                    8, 8, 9, 7, 7, 7, 2, 8, 8, 4, 4, 6, 6, 6, 3, 5, 5, 5, 10,
                    10, 1, 7, 7, 7, 0, 0, 6, 6, 6, 4, 4, 7, 7, 7, 4, 8, 8, 8, 8,
                    2, 2, 7, 7, 7, 7, 3, 3, 10, 10, 10, 8, 8, 8, 8, 9, 7, 7, 7,
                    2, 8, 8, 4, 4, 6, 6, 6, 3, 0, 0, 6, 6, 6, 4, 4, 7, 7, 7
                ],
                [
                    6, 6, 6, 3, 8, 8, 8, 8, 1, 7, 7, 5, 5, 5, 2, 2, 0, 0, 6, 6,
                    6, 9, 8, 8, 8, 3, 3, 10, 10, 10, 10, 5, 5, 5, 1, 1, 6, 6, 6,
                    4, 4, 8, 8, 2, 5, 5, 5, 10, 10, 10, 5, 6, 6, 6, 3, 8, 8, 8,
                    8, 1, 10, 10, 7, 7, 5, 5, 5, 2, 2, 0, 0, 6, 6, 6, 9, 8, 8,
                    8, 3, 3, 10, 10, 10, 5, 5, 5, 1, 1, 6, 6, 6, 4, 4, 10, 10,
                    8, 8, 2, 5, 5, 5, 5
                ],
                [
                    2, 2, 7, 7, 7, 10, 10, 7, 3, 5, 5, 5, 3, 3, 9, 8, 8, 8, 8,
                    2, 10, 10, 5, 5, 5, 5, 1, 1, 0, 0, 7, 7, 4, 4, 10, 10, 6, 6,
                    6, 6, 3, 8, 8, 8, 8, 1, 5, 5, 10, 10, 10, 10, 5, 2, 2, 7, 7,
                    7, 7, 3, 5, 5, 5, 3, 10, 10, 3, 9, 8, 8, 8, 8, 2, 5, 5, 5,
                    5, 1, 1, 0, 0, 7, 7, 4, 4, 6, 6, 6, 6, 3, 10, 10, 8, 8, 8,
                    8, 1, 5, 5, 5
                ]
            ],
            "freeReels": [
                [
                    8, 8, 8, 9, 7, 7, 7, 1, 5, 5, 4, 4, 6, 6, 6, 6, 1, 7, 7, 7,
                    2, 2, 5, 5, 5, 3, 3, 0, 0, 2, 5, 5, 5, 5, 1, 1, 7, 7, 4, 4,
                    6, 6, 6, 3
                ],
                [
                    7, 7, 3, 8, 8, 8, 8, 1, 1, 5, 5, 5, 4, 4, 8, 8, 8, 9, 5, 5,
                    5, 3, 0, 0, 5, 5, 5, 9, 4, 8, 8, 8, 2, 5, 5, 5, 1, 1, 6, 6,
                    6, 4, 4
                ],
                [
                    4, 8, 8, 8, 9, 8, 2, 2, 7, 7, 7, 9, 7, 3, 3, 8, 8, 8, 8, 9,
                    7, 7, 7, 2, 8, 8, 9, 4, 4, 6, 6, 6, 3, 5, 5, 5, 1, 9, 7, 7,
                    7, 0, 0, 6, 6, 6, 9, 4, 4, 7, 7, 7
                ],
                [
                    6, 6, 6, 3, 9, 8, 8, 8, 8, 1, 7, 9, 7, 5, 5, 5, 2, 2, 9, 0,
                    0, 6, 6, 6, 9, 8, 8, 8, 3, 3, 9, 5, 5, 5, 1, 1, 6, 6, 6, 9,
                    4, 4, 8, 8, 2, 5, 5, 5, 5
                ],
                [
                    2, 2, 7, 9, 7, 7, 7, 3, 9, 5, 5, 5, 3, 3, 9, 8, 8, 8, 8, 2,
                    5, 5, 5, 5, 1, 1, 9, 0, 0, 7, 7, 4, 4, 6, 6, 6, 9, 6, 3, 8,
                    8, 8, 8, 1, 5, 5, 5
                ]
            ],
            "baseIncuts": [1, 2, 3, 4, 5, 6, 7, 8],
            "freeIncuts": [1, 2, 3, 4, 5, 6, 7, 8],
            "statTablo": {
                "volatility": 9,
                "bigwin": 9,
                "epicwin": 9,
                "bonus": 7,
                "show": 1,
                "rtp": 96.03
            },
            "maxWinFreq_small": 277841,
            "maxWinFreq_big": 9205002,
            "VIP_maxWinFreq_small": 104508,
            "VIP_maxWinFreq_big": 2183612,
            "arrlimits_winLimitK": [5000, 12000],
            "wildIds": [0],
            "minScatters": [3],
            "scatterIds": [9],
            "incutIds": [16],
            "addBonusK": [10000, 1000, 50, 20],
            "addBonusNames": [
                "wolfThunder_grand",
                "wolfThunder_major",
                "wolfThunder_minor",
                "wolfThunder_mini"
            ],
            "bonusValues": [1, 2, 3, 4, 5],
            "bonusValuesBase": [0, 1, 2, 3, 4, 5, 6, 7, 8, 10, 14, 16, 18],
            "bonusValuesSuper": [
                20, 22, 24, 25, 30, 40, 50, 60, 80, 100, -12, -13, -14, -15
            ],
            "outRates_vipmode": 96.11,
            "volatility": 4.5,
            "sasAdditionalId": "WTH",
            "sasPaytableId": "WTH960"
        },
        "helpInfo": {
            "paytable": [
                [
                    [0, 1],
                    [5, 500],
                    [4, 200],
                    [3, 100]
                ],
                [
                    [1, 4],
                    [5, 300],
                    [4, 150],
                    [3, 75]
                ],
                [
                    [2, 4],
                    [5, 250],
                    [4, 125],
                    [3, 50]
                ],
                [
                    [3, 4],
                    [5, 250],
                    [4, 125],
                    [3, 50]
                ],
                [
                    [4, 4],
                    [5, 200],
                    [4, 100],
                    [3, 25]
                ],
                [
                    [5, 4],
                    [5, 75],
                    [4, 25],
                    [3, 10]
                ],
                [
                    [6, 4],
                    [5, 50],
                    [4, 20],
                    [3, 8]
                ],
                [
                    [7, 4],
                    [5, 50],
                    [4, 20],
                    [3, 8]
                ],
                [
                    [8, 4],
                    [5, 25],
                    [4, 10],
                    [3, 5]
                ],
                [[9, 8]],
                [[10, 8]],
                [[11, 4]],
                [[12, 4]],
                [[13, 4]],
                [[14, 4]],
                [[15, 4]],
                [[16, 16]]
            ],
            "fg": {
                "limit": 40
            },
            "doubles": [["off", 0, 0]]
        },
        "doubleActive": "off",
        "doubleActiveDbSettings": "off",
        "antiDynamiteBet": null,
        "dramshow": null,
        "versions": {
            "server_core": "1.1",
            "server_game": "1.0",
            "server_math": "1.0"
        },
        "winlimits": {
            "maxWinLimitK": 12000,
            "maxWin_gcurr": null,
            "needControlJackpot": true,
            "winLimitK_gameconfig": 12000
        },
        "isMaxFlag": 0,
        "isMaxFlag_lines": 0,
        "linesAssortment": [25],
        "linesPerCredit": 1,
        "reelstate": 0,
        "aux": 0,
        "startBox": [
            [2, 2, 1, 5, 0],
            [2, 2, 5, 2, 0],
            [2, 0, 5, 2, 2],
            [2, 0, 7, 8, 2]
        ],
        "stopBox": [
            [2, 2, 1, 5, 0],
            [2, 2, 5, 2, 0],
            [2, 0, 5, 2, 2],
            [2, 0, 7, 8, 2]
        ],
        "incutId": 2,
        "vipMode": {
            "on": 1,
            "vipBetK": 2,
            "wasBuyVip": 2,
            "vip_noSpecSeed": true
        },
        "setVip_inFreeSpinAlways": -1,
        "bbLimitsWinK": [11505, 5000, 12000, 11505],
        "bonus": {
            "boxIn": [
                [0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0]
            ],
            "boxOut": [
                [0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0],
                [0, 0, 0, 0, 0]
            ],
            "boxWork": null
        },
        "tmpWin": 0,
        "saveBoxIn": [
            [0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0]
        ],
        "shotEffect": false,
        "shotMask": [
            [0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0],
            [0, 0, 0, 0, 0]
        ],
        "helpseed": true
    }`

	result1, err := JSONToGoMapString(jsonStr1)
	if err != nil {
		t.Fatalf("转换失败: %v", err)
	}

	fmt.Println("=== 测试用例1：简单对象 ===")
	fmt.Println(result1)
	fmt.Println()

	// 测试用例2：复杂嵌套对象
	jsonStr2 := `{
		"winValidation": {
			"needcheck": false,
			"winlimit_fictiveRotate_gcurr": 25000000,
			"remaintime": 86400000,
			"period": 86400000,
			"isApproved": false,
			"isNotApproved": false,
			"isWaitApprove": false
		},
		"buyBonus": {
			"wasBuy": 0,
			"selectId": -1,
			"buyTotalBetK": [
				{"id": 0, "cost": 100, "prefix2": "_BASE_FG", "rtp": 96.23},
				{"id": 1, "cost": 500, "prefix2": "_BASE_FG_HOT", "rtp": 96.25}
			]
		}
	}`

	result2, err := JSONToGoMapString(jsonStr2)
	if err != nil {
		t.Fatalf("转换失败: %v", err)
	}

	fmt.Println("=== 测试用例2：复杂嵌套对象 ===")
	fmt.Println(result2)
	fmt.Println()

	// 测试用例3：二维数组
	jsonStr3 := `{
		"startBox": [
			[8, 1, 1, 10, 11],
			[8, 8, 3, 10, 4],
			[5, 4, 8, 10, 8]
		],
		"paytable": [
			[[0, 8]],
			[[1, 1]],
			[[2, 8]]
		]
	}`

	result3, err := JSONToGoMapString(jsonStr3)
	if err != nil {
		t.Fatalf("转换失败: %v", err)
	}

	fmt.Println("=== 测试用例3：二维数组 ===")
	fmt.Println(result3)
	fmt.Println()
}

// ExampleJSONToGoMapString 示例使用函数
func TestExampleJSONToGoMapString(t *testing.T) {
	// 示例JSON字符串
	jsonStr := `{
		"gamegroup": "base",
		"doubleAssortment": ["off"],
		"maxBetPerGame_cents": null,
		"betAssortment": [1, 2, 3, 4, 5, 8, 10, 15, 20, 25, 50, 75, 100],
		"winValidation": {
			"needcheck": false,
			"winlimit_fictiveRotate_gcurr": 25000000,
			"remaintime": 86400000
		},
		"startBox": [
			[8, 1, 1, 10, 11],
			[8, 8, 3, 10, 4],
			[5, 4, 8, 10, 8]
		]
	}`

	// 转换为Go map字符串格式
	result, err := JSONToGoMapString(jsonStr)
	if err != nil {
		fmt.Printf("转换失败: %v\n", err)
		return
	}

	fmt.Println("转换结果:")
	fmt.Println(result)
}
